import React, { useState } from 'react';
import { MessageCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import customerClientChatService from '@/services/customerClientChatService';
import { toast } from 'sonner';

/**
 * OrderChatButton - A button component that initiates order-specific chat
 * Only shows for rejected orders and creates a chat session with the client
 */
export default function OrderChatButton({ 
  order, 
  orderType, 
  className = '',
  variant = 'primary' 
}) {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Only show button for rejected orders and customers
  if (!order || order.status !== 'Rejected' || user?.role !== 'Customer') {
    return null;
  }

  // Get client ID from order based on order type
  const getClientId = () => {
    switch (orderType) {
      case 'cfs_orders':
        // For CFS orders, the client is the CFS provider
        return order.expand?.cfs?.id || order.cfs;
      case 'warehouse_orders':
      case '3pl_orders':
        // For warehouse and 3PL orders, the client is the service provider
        return order.expand?.provider?.id || order.provider;
      case 'transport_orders':
        // For transport orders, the client is the transport provider
        return order.expand?.provider?.id || order.provider;
      default:
        return null;
    }
  };

  const handleChatWithClient = async () => {
    try {
      setIsLoading(true);
      
      const clientId = getClientId();
      if (!clientId) {
        toast.error('Unable to identify the service provider for this order');
        return;
      }

      // Create order-specific chat session
      const chatSession = await customerClientChatService.createOrderChatSession(
        user.id,
        clientId,
        order,
        orderType
      );

      toast.success('Chat session created successfully');
      
      // Navigate to customer chat page
      router.push('/customer/chat');
      
    } catch (error) {
      console.error('Error creating chat session:', error);
      toast.error('Failed to start chat session');
    } finally {
      setIsLoading(false);
    }
  };

  const buttonVariants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50'
  };

  return (
    <button
      onClick={handleChatWithClient}
      disabled={isLoading}
      className={`
        flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors
        ${buttonVariants[variant]}
        ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
    >
      {isLoading ? (
        <Loader2 size={18} className="animate-spin" />
      ) : (
        <MessageCircle size={18} />
      )}
      {isLoading ? 'Starting Chat...' : 'Chat with Client'}
    </button>
  );
}
