import React from 'react'
import { Popover } from '../ui/Popover'
import { <PERSON><PERSON>heckBig, Download, EllipsisVertical, Eye, Trash, MessageCircle } from 'lucide-react'
import Link from 'next/link';
import OrderChatButton from '../ui/OrderChatButton';

export default function RequestsActions({
  row,
  redirectLink = '',
  RejectForm,
  user,
  EditForm,
  deleteItem,
  handleStatusUpdate,
  orderType = null // Add orderType prop to determine which collection this is from
}) {
  return (
    <Popover
      trigger={<EllipsisVertical className="w-4 h-4" />}
      className='w-[250px] p-6'
    >
      <h1 className='font-semibold text-lg p-2 text-left border-b border-foreground/30'>Actions</h1>

      <Link href={redirectLink}>
        <button
          className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
        >
          <p>View Details</p>
          <Eye
            size={18}
            className="cursor-pointer"
          />
        </button>
      </Link>

      <button
        className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
      >
        <p>Download Zip</p>
        <Download
          size={18}
          className="cursor-pointer"
        />
      </button>

      {/* Chat with Client button - Only for rejected orders and customers */}
      {user?.role === 'Customer' && row.original.status === 'Rejected' && orderType && (
        <div className="p-4 border-b border-gray-200">
          <OrderChatButton
            order={row.original}
            orderType={orderType}
            variant="outline"
            className="w-full justify-center"
          />
        </div>
      )}

      {EditForm && <EditForm info={row.original} />}

      <button
        className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
        onClick={async () => {
          console.log('Delete details for', row.original.id);
          const confirmation = confirm('Are you sure you want to delete this entry?');
          if (confirmation) {
            await deleteItem(row.original.id);
          }
        }}
      >
        <p>Delete Entry</p>
        <Trash
          size={18}
          className="cursor-pointer"
        />
      </button>
      {
        (user?.role === 'Root' || user?.role === 'Merchant') && (
          <>
            <button
              className='flex items-center justify-between gap-2 p-4 w-full cursor-pointer'
              onClick={() => handleStatusUpdate(
                row.original.id,
                'Accepted'
              )}
            >
              <p>Accept Request</p>
              <CircleCheckBig
                size={18}
                className="cursor-pointer"
              />
            </button>

            <RejectForm info={row.original} />
          </>
        )
      }
    </Popover>
  )
}

