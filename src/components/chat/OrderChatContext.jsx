import React, { useState, useEffect } from 'react';
import { Package, Calendar, User, MapPin, FileText, AlertCircle } from 'lucide-react';
import customerClientChatService from '@/services/customerClientChatService';

/**
 * OrderChatContext - Displays order information in chat interface
 * Shows relevant order details when chat is linked to a specific order
 */
export default function OrderChatContext({ session }) {
  const [orderDetails, setOrderDetails] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (session?.orderId && session?.orderType) {
        setLoading(true);
        try {
          const order = await customerClientChatService.getOrderDetails(
            session.orderId,
            session.orderType
          );
          setOrderDetails(order);
        } catch (error) {
          console.error('Error fetching order details:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchOrderDetails();
  }, [session?.orderId, session?.orderType]);

  if (!session?.orderId || loading) {
    return null;
  }

  if (!orderDetails) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-center gap-2">
          <AlertCircle size={16} className="text-yellow-600" />
          <span className="text-sm text-yellow-800">
            Unable to load order details for Order ID: {session.orderId}
          </span>
        </div>
      </div>
    );
  }

  const getServiceTypeDisplay = () => {
    const serviceTypeMap = {
      'cfs_orders': 'CFS Service',
      'warehouse_orders': 'Warehouse Service',
      'transport_orders': 'Transport Service',
      '3pl_orders': '3PL Service'
    };
    return serviceTypeMap[session.orderType] || 'Service';
  };

  const getProviderName = () => {
    switch (session.orderType) {
      case 'cfs_orders':
        return orderDetails.expand?.cfs?.title || 'CFS Provider';
      case 'warehouse_orders':
      case '3pl_orders':
        return orderDetails.expand?.provider?.title || 'Service Provider';
      case 'transport_orders':
        return orderDetails.expand?.provider?.title || 'Transport Provider';
      default:
        return 'Service Provider';
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'accepted':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div className="flex items-center gap-2 mb-3">
        <Package size={18} className="text-blue-600" />
        <h3 className="font-semibold text-blue-900">Order Discussion</h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
        <div className="flex items-center gap-2">
          <FileText size={14} className="text-gray-500" />
          <span className="text-gray-600">Order ID:</span>
          <span className="font-mono font-medium">{orderDetails.id}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-gray-600">Service:</span>
          <span className="font-medium">{getServiceTypeDisplay()}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <User size={14} className="text-gray-500" />
          <span className="text-gray-600">Provider:</span>
          <span className="font-medium">{getProviderName()}</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-gray-600">Status:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(orderDetails.status)}`}>
            {orderDetails.status}
          </span>
        </div>

        {/* Order-specific details */}
        {orderDetails.igmNo && (
          <div className="flex items-center gap-2">
            <span className="text-gray-600">IGM No:</span>
            <span className="font-medium">{orderDetails.igmNo}</span>
          </div>
        )}
        
        {orderDetails.blNo && (
          <div className="flex items-center gap-2">
            <span className="text-gray-600">BL No:</span>
            <span className="font-medium">{orderDetails.blNo}</span>
          </div>
        )}

        {orderDetails.consigneeName && (
          <div className="flex items-center gap-2">
            <span className="text-gray-600">Consignee:</span>
            <span className="font-medium">{orderDetails.consigneeName}</span>
          </div>
        )}

        {/* Transport specific fields */}
        {session.orderType === 'transport_orders' && (
          <>
            {orderDetails.startLocation && (
              <div className="flex items-center gap-2">
                <MapPin size={14} className="text-gray-500" />
                <span className="text-gray-600">From:</span>
                <span className="font-medium">{orderDetails.startLocation}</span>
              </div>
            )}
            {orderDetails.endLocation && (
              <div className="flex items-center gap-2">
                <MapPin size={14} className="text-gray-500" />
                <span className="text-gray-600">To:</span>
                <span className="font-medium">{orderDetails.endLocation}</span>
              </div>
            )}
          </>
        )}
      </div>

      {/* Rejection reason if order is rejected */}
      {orderDetails.status === 'Rejected' && orderDetails.reason && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
          <div className="flex items-start gap-2">
            <AlertCircle size={14} className="text-red-600 mt-0.5" />
            <div>
              <span className="text-sm font-medium text-red-800">Rejection Reason:</span>
              <p className="text-sm text-red-700 mt-1">{orderDetails.reason}</p>
            </div>
          </div>
        </div>
      )}

      {/* Order description if available */}
      {orderDetails.orderDescription && (
        <div className="mt-3 p-3 bg-white border border-blue-200 rounded">
          <span className="text-sm font-medium text-blue-800">Order Description:</span>
          <p className="text-sm text-blue-700 mt-1">{orderDetails.orderDescription}</p>
        </div>
      )}
    </div>
  );
}
