var r=require("@emnapi/wasi-threads");const e="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0;function t(r){if(r&&"object"!=typeof r)throw new TypeError("imports must be an object or undefined");return!0}function n(r,a){if(!r)throw new TypeError("Invalid wasm source");t(a),a=null!=a?a:{};try{const e="object"==typeof r&&null!==r&&"then"in r?r.then:void 0;if("function"==typeof e)return e.call(r,(r=>n(r,a)))}catch(r){}if(r instanceof ArrayBuffer||ArrayBuffer.isView(r))return e.instantiate(r,a);if(r instanceof e.Module)return e.instantiate(r,a).then((e=>({instance:e,module:r})));if("undefined"!=typeof Response&&r instanceof Response)return r.arrayBuffer().then((r=>e.instantiate(r,a)));const o="string"==typeof r;if(o||"undefined"!=typeof URL&&r instanceof URL){if(o&&"undefined"!=typeof wx&&"undefined"!=typeof __wxConfig)return e.instantiate(r,a);if("function"!=typeof fetch)throw new TypeError("wasm source can not be a string or URL in this environment");if("function"!=typeof e.instantiateStreaming)return n(fetch(r),a);try{return e.instantiateStreaming(fetch(r),a).catch((()=>n(fetch(r),a)))}catch(e){return n(fetch(r),a)}}throw new TypeError("Invalid wasm source")}function a(t){var n=function(){var n,a,o,s,i,u="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node,f=Boolean(t.childThread),c="number"==typeof t.waitThreadStart?t.waitThreadStart:Boolean(t.waitThreadStart);function l(r){if("function"==typeof e.RuntimeError)throw new e.RuntimeError(r);throw Error(r)}var d,v,p,g={imports:{env:{},napi:{},emnapi:{}},exports:{},emnapi:{},loaded:!1,filename:"",childThread:f,initWorker:void 0,executeAsyncWork:void 0,waitThreadStart:c,PThread:void 0,init:function(r){if(g.loaded)return g.exports;if(!r)throw new TypeError("Invalid napi init options");var t=r.instance;if(!(null==t?void 0:t.exports))throw new TypeError("Invalid wasm instance");n=t;var u=t.exports,f=r.module,c=r.memory||u.memory,p=r.table||u.__indirect_function_table;if(!(f instanceof e.Module))throw new TypeError("Invalid wasm module");if(!(c instanceof e.Memory))throw new TypeError("Invalid wasm memory");if(!(p instanceof e.Table))throw new TypeError("Invalid wasm table");if(a=c,o=p,"function"!=typeof u.malloc)throw new TypeError("malloc is not exported");if("function"!=typeof u.free)throw new TypeError("free is not exported");if(s=u.malloc,i=u.free,!g.childThread){var y=8,h=t.exports.node_api_module_get_api_version_v1;"function"==typeof h&&(y=h());var E=g.envObject||(g.envObject=d.createEnv(g.filename,y,(function(r){return o.get(r)}),(function(r){return o.get(r)}),l,v)),_=d.openScope(E);try{E.callIntoModule((function(r){var e=g.exports,n=_.add(e),a=(0,t.exports.napi_register_wasm_v1)(r.id,n.id);g.exports=a?d.handleStore.get(a).value:e}))}finally{d.closeScope(E,_)}return g.loaded=!0,delete g.envObject,g.exports}}},y=void 0;if(f){d=null==t?void 0:t.context;var h="function"==typeof t.postMessage?t.postMessage:"function"==typeof postMessage?postMessage:void 0;if("function"!=typeof h)throw new TypeError("No postMessage found");g.postMessage=h}else{var E=t.context;if("object"!=typeof E||null===E)throw new TypeError("Invalid `options.context`. Use `import { getDefaultContext } from '@emnapi/runtime'`");d=E}if("string"==typeof t.filename&&(g.filename=t.filename),"function"==typeof t.onCreateWorker&&(y=t.onCreateWorker),"function"==typeof t.print?t.print:console.log.bind(console),p="function"==typeof t.printErr?t.printErr:console.warn.bind(console),"nodeBinding"in t){var _=t.nodeBinding;if("object"!=typeof _||null===_)throw new TypeError("Invalid `options.nodeBinding`. Use @emnapi/node-binding package");v=_}var w=0;if("asyncWorkPoolSize"in t){if("number"!=typeof t.asyncWorkPoolSize)throw new TypeError("options.asyncWorkPoolSize must be a integer");(w=t.asyncWorkPoolSize|0)>1024?w=1024:w<-1024&&(w=-1024)}var L=!f&&w<=0;function m(){return Math.abs(w)}function b(r){if(!r)return!1;if(r._emnapiSendListener)return!0;var e=function(r){var e=(u?r:r.data).__emnapi__;if(e&&"async-send"===e.type)if(f){(0,g.postMessage)({__emnapi__:e})}else{var t=e.payload.callback;o.get(t)(e.payload.data)}};return r._emnapiSendListener={handler:e,dispose:function(){u?r.off("message",e):r.removeEventListener("message",e,!1),delete r._emnapiSendListener}},u?r.on("message",e):r.addEventListener("message",e,!1),!0}g.imports.env._emnapi_async_work_pool_size=m,g.emnapi.addSendListener=b;var S=new r.ThreadManager(f?{printErr:p,childThread:!0}:{printErr:p,beforeLoad:function(r){b(r)},reuseWorker:t.reuseWorker,onCreateWorker:y});function C(r,e){d.feature.setImmediate((function(){o.get(r)(e)}))}function I(r,e){Promise.resolve().then((function(){o.get(r)(e)}))}function A(r,e){var t,n=[e>>>0,(t=e,+Math.abs(t)>=1?t>0?(0|Math.min(+Math.floor(t/4294967296),4294967295))>>>0:~~+Math.ceil((t-+(~~t>>>0))/4294967296)>>>0:0)],o=new DataView(a.buffer);o.setInt32(r,n[0],!0),o.setInt32(r+4,n[1],!0)}g.PThread=S;var k,V=Object.freeze({__proto__:null,$emnapiSetValueI64:A,_emnapi_call_finalizer:function(r,e,t,n,a){d.envStore.get(e).callFinalizerInternal(r,t,n,a)},_emnapi_callback_into_module:function(r,e,t,n,a){var s=d.envStore.get(e),i=d.openScope(s);try{s.callbackIntoModule(Boolean(r),(function(){o.get(t)(e,n)}))}catch(r){throw d.closeScope(s,i),a&&d.closeScope(s),r}d.closeScope(s,i)},_emnapi_ctx_decrease_waiting_request_counter:function(){d.decreaseWaitingRequestCounter()},_emnapi_ctx_increase_waiting_request_counter:function(){d.increaseWaitingRequestCounter()},_emnapi_get_node_version:function(r,e,t){var n="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node?process.versions.node.split(".").map((function(r){return Number(r)})):[0,0,0],o=new DataView(a.buffer);o.setUint32(r,n[0],!0),o.setUint32(e,n[1],!0),o.setUint32(t,n[2],!0)},_emnapi_next_tick:I,_emnapi_runtime_keepalive_pop:function(){},_emnapi_runtime_keepalive_push:function(){},_emnapi_set_immediate:C,napi_clear_last_error:function(r){return d.envStore.get(r).clearLastError()},napi_set_last_error:function(r,e,t,n){return d.envStore.get(r).setLastError(e,t,n)}});function T(r){var e=new DataView(a.buffer).getInt32(r+20,!0);return S.pthreads[e]}var D=new Promise((function(r){k=function(){D.ready=!0,r()}}));D.ready=!1;var B=Object.freeze({__proto__:null,_emnapi_after_uvthreadpool_ready:function(r,e,t){D.ready?o.get(r)(e,t):D.then((function(){o.get(r)(e,t)}))},_emnapi_async_send_js:function(r,e,t){if(f)(0,g.postMessage)({__emnapi__:{type:"async-send",payload:{callback:e,data:t}}});else switch(r){case 0:C(e,t);break;case 1:I(e,t)}},_emnapi_emit_async_thread_ready:function(){f&&(0,g.postMessage)({__emnapi__:{type:"async-thread-ready",payload:{}}})},_emnapi_is_main_browser_thread:function(){return"undefined"==typeof window||"undefined"==typeof document||u?0:1},_emnapi_tell_js_uvthreadpool:function(r,e){for(var t=[],n=new DataView(a.buffer),o=function(e){var a=T(n.getInt32(r+4*e,!0));t.push(new Promise((function(r){var e=function(t){var n=(u?t:t.data).__emnapi__;n&&"async-thread-ready"===n.type&&(r(),a&&"function"==typeof a.unref&&a.unref(),u?a.off("message",e):a.removeEventListener("message",e))};u?a.on("message",e):a.addEventListener("message",e)})))},s=0;s<e;s++)o(s);Promise.all(t).then(k)},_emnapi_worker_unref:function(r){if(!f){var e=T(r);e&&"function"==typeof e.unref&&e.unref()}}});var x=Object.freeze({__proto__:null,napi_adjust_external_memory:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(!t)return n.setLastError(1);var o=Number(e);if(o<0)return n.setLastError(1);var s=a.buffer.byteLength,i=s+o;return i+=(65536-i%65536)%65536,-1===a.grow(i-s+65535>>16)?n.setLastError(9):(d.feature.supportBigInt?new DataView(a.buffer).setBigInt64(t,BigInt(a.buffer.byteLength),!0):A(t,a.buffer.byteLength),n.clearLastError())}}),U={idGen:{},values:[void 0],queued:new Set,pending:[],init:function(){var r={nextId:1,list:[],generate:function(){var e;return r.list.length?e=r.list.shift():(e=r.nextId,r.nextId++),e},reuse:function(e){r.list.push(e)}};U.idGen=r,U.values=[void 0],U.queued=new Set,U.pending=[]},create:function(r,e,t,n,a,o){var s=0,i=0;if(v){var u=v.node.emitAsyncInit(e,t,-1);s=u.asyncId,i=u.triggerAsyncId}var f=U.idGen.generate();return U.values[f]={env:r,id:f,resource:e,asyncId:s,triggerAsyncId:i,status:0,execute:n,complete:a,data:o},f},callComplete:function(r,e){var t=r.complete,n=r.env,a=r.data,s=function(){if(t){var r=d.envStore.get(n),s=d.openScope(r);try{r.callbackIntoModule(!0,(function(){o.get(t)(n,e,a)}))}finally{d.closeScope(r,s)}}};v?v.node.makeCallback(r.resource,s,[],{asyncId:r.asyncId,triggerAsyncId:r.triggerAsyncId}):s()},queue:function(r){var e=U.values[r];if(e&&0===e.status){if(e.status=1,U.queued.size>=(Math.abs(w)||4))return void U.pending.push(r);U.queued.add(r);var t=e.env,n=e.data,a=e.execute;e.status=2,d.feature.setImmediate((function(){if(o.get(a)(t,n),U.queued.delete(r),e.status=3,d.feature.setImmediate((function(){U.callComplete(e,0)})),U.pending.length>0){var s=U.pending.shift();U.values[s].status=0,U.queue(s)}}))}},cancel:function(r){var e=U.pending.indexOf(r);if(-1!==e){var t=U.values[r];return t&&1===t.status?(t.status=4,U.pending.splice(e,1),d.feature.setImmediate((function(){U.callComplete(t,11)})),0):9}return 9},remove:function(r){var e=U.values[r];e&&(v&&v.node.emitAsyncDestroy({asyncId:e.asyncId,triggerAsyncId:e.triggerAsyncId}),U.values[r]=void 0,U.idGen.reuse(r))}};function R(r,e,t,n){if(v){var o=d.handleStore.get(r).value,s=d.handleStore.get(e).value,i=v.node.emitAsyncInit(o,s,t),u=i.asyncId,f=i.triggerAsyncId;if(n){var c=new DataView(a.buffer);c.setFloat64(n,u,!0),c.setFloat64(n+8,f,!0)}}}function j(r,e){v&&v.node.emitAsyncDestroy({asyncId:r,triggerAsyncId:e})}var G=Object.freeze({__proto__:null,_emnapi_async_destroy_js:function(r){if(!v)return 9;var e=new DataView(a.buffer),t=e.getInt32(r,!0),n=e.getInt32(r+4,!0),o=BigInt(t>>>0)|BigInt(n)<<BigInt(32),s=v.napi.asyncDestroy(o);return 0!==s.status?s.status:0},_emnapi_async_init_js:function(r,e,t){if(!v)return 9;var n;r&&(n=Object(d.handleStore.get(r).value));var o=d.handleStore.get(e).value,s=v.napi.asyncInit(n,o);if(0!==s.status)return s.status;var i=s.value;i>=BigInt(-1)*(BigInt(1)<<BigInt(63))&&i<BigInt(1)<<BigInt(63)||(i&=(BigInt(1)<<BigInt(64))-BigInt(1))>=BigInt(1)<<BigInt(63)&&(i-=BigInt(1)<<BigInt(64));var u=Number(i&BigInt(4294967295)),f=Number(i>>BigInt(32)),c=new DataView(a.buffer);return c.setInt32(t,u,!0),c.setInt32(t+4,f,!0),0},_emnapi_env_check_gc_access:function(r){d.envStore.get(r).checkGCAccess()},_emnapi_node_emit_async_destroy:j,_emnapi_node_emit_async_init:R,_emnapi_node_make_callback:function(r,e,t,n,o,s,i,u){var f,c=0;if(v){var l=d.handleStore.get(e).value,p=d.handleStore.get(t).value;o>>>=0;for(var g=Array(o),y=new DataView(a.buffer);c<o;c++){var h=y.getInt32(n+4*c,!0);g[c]=d.handleStore.get(h).value}var E=v.node.makeCallback(l,p,g,{asyncId:s,triggerAsyncId:i});if(u)f=d.envStore.get(r).ensureHandleId(E),y.setInt32(u,f,!0)}},napi_close_callback_scope:function(r,e){throw new Error("napi_close_callback_scope has not been implemented yet")},napi_make_callback:function(r,e,t,n,o,s,i){var u,f=0;if(!r)return 1;var c=d.envStore.get(r);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!v)return c.setLastError(9);if(!t)return c.setLastError(1);if(o>0&&!s)return c.setLastError(1);var l=Object(d.handleStore.get(t).value),p=d.handleStore.get(n).value;if("function"!=typeof p)return c.setLastError(1);var g=new DataView(a.buffer),y=g.getInt32(e,!0),h=g.getInt32(e+4,!0),E=BigInt(y>>>0)|BigInt(h)<<BigInt(32);o>>>=0;for(var _=Array(o);f<o;f++){var w=g.getInt32(s+4*f,!0);_[f]=d.handleStore.get(w).value}var L=v.napi.makeCallback(E,l,p,_);if(L.error)throw L.error;return 0!==L.status?c.setLastError(L.status):(i&&(u=c.ensureHandleId(L.value),g.setInt32(i,u,!0)),c.getReturnStatus())}catch(r){return c.tryCatch.setError(r),c.setLastError(10)}},napi_open_callback_scope:function(r,e,t,n){throw new Error("napi_open_callback_scope has not been implemented yet")}}),z={offset:{resource:0,async_id:8,trigger_async_id:16,queue_size:24,queue:28,thread_count:32,is_closing:36,dispatch_state:40,context:44,max_queue_size:48,ref:52,env:56,finalize_data:60,finalize_cb:64,call_js_cb:68,handles_closing:72,async_ref:76,mutex:80,cond:84,end:88},init:function(){if(void 0!==S){S.unusedWorkers.forEach(z.addListener),S.runningWorkers.forEach(z.addListener);var r=S.getNewWorker;S.getNewWorker=function(){var e=r.apply(this,arguments);return z.addListener(e),e}}},addListener:function(r){if(!r)return!1;if(r._emnapiTSFNListener)return!0;var e=function(r){var e=(u?r:r.data).__emnapi__;if(e){var t=e.type,n=e.payload;"tsfn-send"===t&&z.dispatch(n.tsfn)}};return r._emnapiTSFNListener={handler:e,dispose:function(){u?r.off("message",e):r.removeEventListener("message",e,!1),delete r._emnapiTSFNListener}},u?r.on("message",e):r.addEventListener("message",e,!1),!0},initQueue:function(r){var e=s(8);return!!e&&(new Uint8Array(a.buffer,e,8).fill(0),z.storeSizeTypeValue(r+z.offset.queue,e,!1),!0)},destroyQueue:function(r){var e=z.loadSizeTypeValue(r+z.offset.queue,!1);e&&i(e)},pushQueue:function(r,e){var t=z.loadSizeTypeValue(r+z.offset.queue,!1),n=z.loadSizeTypeValue(t,!1),a=z.loadSizeTypeValue(t+4,!1),o=s(8);if(!o)throw new Error("OOM");z.storeSizeTypeValue(o,e,!1),z.storeSizeTypeValue(o+4,0,!1),0===n&&0===a?(z.storeSizeTypeValue(t,o,!1),z.storeSizeTypeValue(t+4,o,!1)):(z.storeSizeTypeValue(a+4,o,!1),z.storeSizeTypeValue(t+4,o,!1)),z.addQueueSize(r)},shiftQueue:function(r){var e=z.loadSizeTypeValue(r+z.offset.queue,!1),t=z.loadSizeTypeValue(e,!1);if(0===t)return 0;var n=t,a=z.loadSizeTypeValue(t+4,!1);z.storeSizeTypeValue(e,a,!1),0===a&&z.storeSizeTypeValue(e+4,0,!1),z.storeSizeTypeValue(n+4,0,!1);var o=z.loadSizeTypeValue(n,!1);return i(n),z.subQueueSize(r),o},push:function(r,e,t){var n=z.getMutex(r),a=z.getCond(r),o="undefined"!=typeof window&&"undefined"!=typeof document&&!u;return n.execute((function(){for(;n=void 0,s=void 0,i=void 0,n=z.getQueueSize(r),s=z.getMaxQueueSize(r),i=z.getIsClosing(r),n>=s&&s>0&&!i;){if(0===t)return 15;if(o)return 21;a.wait()}var n,s,i;return z.getIsClosing(r)?0===z.getThreadCount(r)?1:(z.subThreadCount(r),16):(z.pushQueue(r,e),z.send(r),0)}))},getMutex:function(r){var e=r+z.offset.mutex,t={lock:function(){var r="undefined"!=typeof window&&"undefined"!=typeof document&&!u,t=new Int32Array(a.buffer,e,1);if(r)for(;;){if(0===Atomics.compareExchange(t,0,0,1))return}else for(;;){if(0===Atomics.compareExchange(t,0,0,1))return;Atomics.wait(t,0,1)}},unlock:function(){var r=new Int32Array(a.buffer,e,1);if(1!==Atomics.compareExchange(r,0,1,0))throw new Error("Tried to unlock while not holding the mutex");Atomics.notify(r,0,1)},execute:function(r){t.lock();try{return r()}finally{t.unlock()}}};return t},getCond:function(r){var e=r+z.offset.cond,t=z.getMutex(r);return{wait:function(){var r=new Int32Array(a.buffer,e,1),n=Atomics.load(r,0);t.unlock(),Atomics.wait(r,0,n),t.lock()},signal:function(){var r=new Int32Array(a.buffer,e,1);Atomics.add(r,0,1),Atomics.notify(r,0,1)}}},getQueueSize:function(r){return z.loadSizeTypeValue(r+z.offset.queue_size,!0)},addQueueSize:function(r){var e,t,n=z.offset.queue_size;e=new Uint32Array(a.buffer),t=r+n>>2,Atomics.add(e,t,1)},subQueueSize:function(r){var e,t,n=z.offset.queue_size;e=new Uint32Array(a.buffer),t=r+n>>2,Atomics.sub(e,t,1)},getThreadCount:function(r){return z.loadSizeTypeValue(r+z.offset.thread_count,!0)},addThreadCount:function(r){var e,t,n=z.offset.thread_count;e=new Uint32Array(a.buffer),t=r+n>>2,Atomics.add(e,t,1)},subThreadCount:function(r){var e,t,n=z.offset.thread_count;e=new Uint32Array(a.buffer),t=r+n>>2,Atomics.sub(e,t,1)},getIsClosing:function(r){return Atomics.load(new Int32Array(a.buffer),r+z.offset.is_closing>>2)},setIsClosing:function(r,e){Atomics.store(new Int32Array(a.buffer),r+z.offset.is_closing>>2,e)},getHandlesClosing:function(r){return Atomics.load(new Int32Array(a.buffer),r+z.offset.handles_closing>>2)},setHandlesClosing:function(r,e){Atomics.store(new Int32Array(a.buffer),r+z.offset.handles_closing>>2,e)},getDispatchState:function(r){return Atomics.load(new Uint32Array(a.buffer),r+z.offset.dispatch_state>>2)},getContext:function(r){return z.loadSizeTypeValue(r+z.offset.context,!1)},getMaxQueueSize:function(r){return z.loadSizeTypeValue(r+z.offset.max_queue_size,!0)},getEnv:function(r){return z.loadSizeTypeValue(r+z.offset.env,!1)},getCallJSCb:function(r){return z.loadSizeTypeValue(r+z.offset.call_js_cb,!1)},getRef:function(r){return z.loadSizeTypeValue(r+z.offset.ref,!1)},getResource:function(r){return z.loadSizeTypeValue(r+z.offset.resource,!1)},getFinalizeCb:function(r){return z.loadSizeTypeValue(r+z.offset.finalize_cb,!1)},getFinalizeData:function(r){return z.loadSizeTypeValue(r+z.offset.finalize_data,!1)},loadSizeTypeValue:function(r,e){var t;return e?(t=new Uint32Array(a.buffer),Atomics.load(t,r>>2)):(t=new Int32Array(a.buffer),Atomics.load(t,r>>2))},storeSizeTypeValue:function(r,e,t){var n;return t?(n=new Uint32Array(a.buffer),void Atomics.store(n,r>>2,e)):(n=new Int32Array(a.buffer),void Atomics.store(n,r>>2,e>>>0))},destroy:function(r){z.destroyQueue(r);var e=z.getEnv(r),t=d.envStore.get(e),n=z.getRef(r);n&&d.refStore.get(n).dispose(),d.removeCleanupHook(t,z.cleanup,r),t.unref();var o=r+z.offset.async_ref>>2,s=new Int32Array(a.buffer);Atomics.load(s,o)&&(Atomics.store(s,o,0),d.decreaseWaitingRequestCounter());var u=z.getResource(r);if(d.refStore.get(u).dispose(),v){var f=new DataView(a.buffer);j(f.getFloat64(r+z.offset.async_id,!0),f.getFloat64(r+z.offset.trigger_async_id,!0))}i(r)},emptyQueueAndDelete:function(r){for(var e,t=z.getCallJSCb(r),n=z.getContext(r);z.getQueueSize(r)>0;)e=z.shiftQueue(r),t&&o.get(t)(0,0,n,e);z.destroy(r)},finalize:function(r){var e=z.getEnv(r),t=d.envStore.get(e);d.openScope(t);var n=z.getFinalizeCb(r),o=z.getFinalizeData(r),s=z.getContext(r),i=function(){t.callFinalizerInternal(0,n,o,s)};try{if(n)if(v){var u=z.getResource(r),f=d.refStore.get(u).get(),c=d.handleStore.get(f).value,l=new DataView(a.buffer),p=l.getFloat64(r+z.offset.async_id,!0),g=l.getFloat64(r+z.offset.trigger_async_id,!0);v.node.makeCallback(c,i,[],{asyncId:p,triggerAsyncId:g})}else i();z.emptyQueueAndDelete(r)}finally{d.closeScope(t)}},cleanup:function(r){z.closeHandlesAndMaybeDelete(r,1)},closeHandlesAndMaybeDelete:function(r,e){var t=z.getEnv(r),n=d.envStore.get(t);d.openScope(n);try{if(e&&z.getMutex(r).execute((function(){z.setIsClosing(r,1),z.getMaxQueueSize(r)>0&&z.getCond(r).signal()})),z.getHandlesClosing(r))return;z.setHandlesClosing(r,1),d.feature.setImmediate((function(){z.finalize(r)}))}finally{d.closeScope(n)}},dispatchOne:function(r){var e=0,t=!1,n=!1,s=z.getMutex(r),i=z.getCond(r);if(s.execute((function(){if(z.getIsClosing(r))z.closeHandlesAndMaybeDelete(r,0);else{var a=z.getQueueSize(r);if(a>0){e=z.shiftQueue(r),t=!0;var o=z.getMaxQueueSize(r);a===o&&o>0&&i.signal(),a--}0===a?0===z.getThreadCount(r)&&(z.setIsClosing(r,1),z.getMaxQueueSize(r)>0&&i.signal(),z.closeHandlesAndMaybeDelete(r,0)):n=!0}})),t){var u=z.getEnv(r),f=d.envStore.get(u);d.openScope(f);var c=function(){f.callbackIntoModule(!1,(function(){var t=z.getCallJSCb(r),n=z.getRef(r),a=n?d.refStore.get(n).get():0;if(t){var s=z.getContext(r);o.get(t)(u,a,s,e)}else{var i=a?d.handleStore.get(a).value:null;"function"==typeof i&&i()}}))};try{if(v){var l=z.getResource(r),p=d.refStore.get(l).get(),g=d.handleStore.get(p).value,y=new DataView(a.buffer);v.node.makeCallback(g,c,[],{asyncId:y.getFloat64(r+z.offset.async_id,!0),triggerAsyncId:y.getFloat64(r+z.offset.trigger_async_id,!0)})}else c()}finally{d.closeScope(f)}}return n},dispatch:function(r){for(var e=!0,t=1e3,n=new Uint32Array(a.buffer),o=r+z.offset.dispatch_state>>2;e&&0!=--t;)Atomics.store(n,o,1),e=z.dispatchOne(r),1!==Atomics.exchange(n,o,0)&&(e=!0);e&&z.send(r)},send:function(r){1&~Atomics.or(new Uint32Array(a.buffer),r+z.offset.dispatch_state>>2,2)&&(void 0!==f&&f?postMessage({__emnapi__:{type:"tsfn-send",payload:{tsfn:r}}}):d.feature.setImmediate((function(){z.dispatch(r)})))}};var O={unusedWorkers:[],runningWorkers:[],workQueue:[],workerReady:null,offset:{resource:0,async_id:8,trigger_async_id:16,env:24,data:28,execute:32,complete:36,end:40},init:function(){O.unusedWorkers=[],O.runningWorkers=[],O.workQueue=[],O.workerReady=null},addListener:function(r){if(!r)return!1;if(r._emnapiAWMTListener)return!0;var e=function(e){var t=(u?e:e.data).__emnapi__;if(t){var n=t.type,a=t.payload;"async-work-complete"===n?(d.decreaseWaitingRequestCounter(),O.runningWorkers.splice(O.runningWorkers.indexOf(r),1),O.unusedWorkers.push(r),O.checkIdleWorker(),O.callComplete(a.work,0)):"async-work-queue"===n?O.scheduleWork(a.work):"async-work-cancel"===n&&O.cancelWork(a.work)}};return r._emnapiAWMTListener={handler:e,dispose:function(){u?r.off("message",e):r.removeEventListener("message",e,!1),delete r._emnapiAWMTListener}},u?r.on("message",e):r.addEventListener("message",e,!1),!0},initWorkers:function(r){if(f)return O.workerReady||(O.workerReady=Promise.resolve());if(O.workerReady)return O.workerReady;if("function"!=typeof y)throw new TypeError("`options.onCreateWorker` is not a function");var e=[],t=[];if(!("emnapi_async_worker_create"in n.exports))throw new TypeError("`emnapi_async_worker_create` is not exported, please try to add `--export=emnapi_async_worker_create` to linker flags");for(var a=0;a<r;++a)t.push(n.exports.emnapi_async_worker_create());try{var o=function(r){var n=y({type:"async-work",name:"emnapi-async-worker"}),a=S.loadWasmModuleToWorker(n);O.addListener(n),e.push(a.then((function(){"function"==typeof n.unref&&n.unref()}))),O.unusedWorkers.push(n);var o=t[r];n.threadBlockBase=o,n.postMessage({__emnapi__:{type:"async-worker-init",payload:{arg:o}}})};for(a=0;a<r;++a)o(a)}catch(e){for(a=0;a<r;++a){var s=t[a];i(s)}throw e}return O.workerReady=Promise.all(e),O.workerReady},checkIdleWorker:function(){if(O.unusedWorkers.length>0&&O.workQueue.length>0){var r=O.unusedWorkers.shift(),e=O.workQueue.shift();O.runningWorkers.push(r),r.postMessage({__emnapi__:{type:"async-work-execute",payload:{work:e}}})}},getResource:function(r){return z.loadSizeTypeValue(r+O.offset.resource,!1)},getExecute:function(r){return z.loadSizeTypeValue(r+O.offset.execute,!1)},getComplete:function(r){return z.loadSizeTypeValue(r+O.offset.complete,!1)},getEnv:function(r){return z.loadSizeTypeValue(r+O.offset.env,!1)},getData:function(r){return z.loadSizeTypeValue(r+O.offset.data,!1)},scheduleWork:function(r){var e;if(f)(0,g.postMessage)({__emnapi__:{type:"async-work-queue",payload:{work:r}}});else if(d.increaseWaitingRequestCounter(),O.workQueue.push(r),null===(e=O.workerReady)||void 0===e?void 0:e.ready)O.checkIdleWorker();else{var t=function(r){throw d.decreaseWaitingRequestCounter(),r};try{O.initWorkers(m()).then((function(){O.workerReady.ready=!0,O.checkIdleWorker()}),t)}catch(r){t(r)}}},cancelWork:function(r){if(f)return(0,g.postMessage)({__emnapi__:{type:"async-work-cancel",payload:{work:r}}}),0;var e=O.workQueue.indexOf(r);return-1!==e?(O.workQueue.splice(e,1),d.feature.setImmediate((function(){d.decreaseWaitingRequestCounter(),O.checkIdleWorker(),O.callComplete(r,11)})),0):9},callComplete:function(r,e){var t=O.getComplete(r),n=O.getEnv(r),s=O.getData(r),i=d.envStore.get(n),u=d.openScope(i),f=function(){t&&i.callbackIntoModule(!0,(function(){o.get(t)(n,e,s)}))};try{if(v){var c=O.getResource(r),l=d.refStore.get(c).get(),p=d.handleStore.get(l).value,g=new DataView(a.buffer),y=g.getFloat64(r+O.offset.async_id,!0),h=g.getFloat64(r+O.offset.trigger_async_id,!0);v.node.makeCallback(p,f,[],{asyncId:y,triggerAsyncId:h})}else f()}finally{d.closeScope(i,u)}}},M=L?function(r,e,t,n,o,s,i){if(!r)return 1;var u,f=d.envStore.get(r);if(f.checkGCAccess(),!n)return f.setLastError(1);if(!i)return f.setLastError(1);if(u=e?Object(d.handleStore.get(e).value):{},!t)return f.setLastError(1);var c=String(d.handleStore.get(t).value),l=U.create(r,u,c,n,o,s);return new DataView(a.buffer).setInt32(i,l,!0),f.clearLastError()}:function(r,e,t,n,o,i,u){if(!r)return 1;var f,c=d.envStore.get(r);if(c.checkGCAccess(),!n)return c.setLastError(1);if(!u)return c.setLastError(1);if(f=e?Object(d.handleStore.get(e).value):{},!t)return c.setLastError(1);var l=O.offset.end,v=s(l);if(!v)return c.setLastError(9);new Uint8Array(a.buffer).subarray(v,v+l).fill(0);var p=c.ensureHandleId(f),g=d.createReference(c,p,1,1).id,y=new DataView(a.buffer);return y.setInt32(v,g,!0),R(p,t,-1,v+O.offset.async_id),y.setInt32(v+O.offset.env,r,!0),y.setInt32(v+O.offset.execute,n,!0),y.setInt32(v+O.offset.complete,o,!0),y.setInt32(v+O.offset.data,i,!0),y.setInt32(u,v,!0),c.clearLastError()},F=L?function(r,e){if(!r)return 1;var t=d.envStore.get(r);return t.checkGCAccess(),e?(U.remove(e),t.clearLastError()):t.setLastError(1)}:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=O.getResource(e);if(d.refStore.get(n).dispose(),v){var o=new DataView(a.buffer);j(o.getFloat64(e+O.offset.async_id,!0),o.getFloat64(e+O.offset.trigger_async_id,!0))}return i(e),t.clearLastError()},W=L?function(r,e){if(!r)return 1;var t=d.envStore.get(r);return e?(U.queue(e),t.clearLastError()):t.setLastError(1)}:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return e?(O.scheduleWork(e),t.clearLastError()):t.setLastError(1)},N=L?function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(!e)return t.setLastError(1);var n=U.cancel(e);return 0===n?t.clearLastError():t.setLastError(n)}:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(!e)return t.setLastError(1);var n=O.cancelWork(e);return 0===n?t.clearLastError():t.setLastError(n)};g.initWorker=function(r){if(!g.childThread)throw new Error("startThread is only available in child threads");if("function"!=typeof n.exports.emnapi_async_worker_init)throw new TypeError("`emnapi_async_worker_init` is not exported, please try to add `--export=emnapi_async_worker_init` to linker flags");n.exports.emnapi_async_worker_init(r)},g.executeAsyncWork=function(r){if(f){var e=O.getExecute(r),t=O.getEnv(r),n=O.getData(r);o.get(e)(t,n),(0,g.postMessage)({__emnapi__:{type:"async-work-complete",payload:{work:r}}})}};var J=Object.freeze({__proto__:null,napi_cancel_async_work:N,napi_create_async_work:M,napi_delete_async_work:F,napi_queue_async_work:W}),P={registry:"function"==typeof FinalizationRegistry?new FinalizationRegistry((function(r){i(r)})):void 0,table:new WeakMap,wasmMemoryViewTable:new WeakMap,init:function(){P.registry="function"==typeof FinalizationRegistry?new FinalizationRegistry((function(r){i(r)})):void 0,P.table=new WeakMap,P.wasmMemoryViewTable=new WeakMap},isDetachedArrayBuffer:function(r){if(0===r.byteLength)try{new Uint8Array(r)}catch(r){return!0}return!1},getArrayBufferPointer:function(r,e){var t,n={address:0,ownership:0,runtimeAllocated:0};if(r===a.buffer)return n;var o=P.isDetachedArrayBuffer(r);if(P.table.has(r)){var i=P.table.get(r);return o?(i.address=0,i):(e&&0===i.ownership&&1===i.runtimeAllocated&&new Uint8Array(a.buffer).set(new Uint8Array(r),i.address),i)}if(o||0===r.byteLength)return n;if(!e)return n;var u=s(r.byteLength);if(!u)throw new Error("Out of memory");return new Uint8Array(a.buffer).set(new Uint8Array(r),u),n.address=u,n.ownership=P.registry?0:1,n.runtimeAllocated=1,P.table.set(r,n),null===(t=P.registry)||void 0===t||t.register(r,u),n},getOrUpdateMemoryView:function(r){if(r.buffer===a.buffer)return P.wasmMemoryViewTable.has(r)||P.wasmMemoryViewTable.set(r,{Ctor:r.constructor,address:r.byteOffset,length:r instanceof DataView?r.byteLength:r.length,ownership:1,runtimeAllocated:0}),r;if((P.isDetachedArrayBuffer(r.buffer)||"function"==typeof SharedArrayBuffer&&r.buffer instanceof SharedArrayBuffer)&&P.wasmMemoryViewTable.has(r)){var e=P.wasmMemoryViewTable.get(r),t=e.Ctor,n=void 0,o=d.feature.Buffer;return n="function"==typeof o&&t===o?o.from(a.buffer,e.address,e.length):new t(a.buffer,e.address,e.length),P.wasmMemoryViewTable.set(n,e),n}return r},getViewPointer:function(r,e){if((r=P.getOrUpdateMemoryView(r)).buffer===a.buffer){if(P.wasmMemoryViewTable.has(r)){var t=P.wasmMemoryViewTable.get(r);return{address:t.address,ownership:t.ownership,runtimeAllocated:t.runtimeAllocated,view:r}}return{address:r.byteOffset,ownership:1,runtimeAllocated:0,view:r}}var n=P.getArrayBufferPointer(r.buffer,e),o=n.address,s=n.ownership,i=n.runtimeAllocated;return{address:0===o?0:o+r.byteOffset,ownership:s,runtimeAllocated:i,view:r}}},q={utf8Decoder:void 0,utf16Decoder:void 0,init:function(){var r,e={decode:function(r){for(var e=0,t=Math.min(4096,r.length+1),n=new Uint16Array(t),a=[],o=0;;){var s=e<r.length;if(!s||o>=t-1){var i=n.subarray(0,o);if(a.push(String.fromCharCode.apply(null,i)),!s)return a.join("");r=r.subarray(e),e=0,o=0}var u=r[e++];if(128&u){if(192==(224&u)){var f=63&r[e++];n[o++]=(31&u)<<6|f}else if(224==(240&u)){f=63&r[e++];var c=63&r[e++];n[o++]=(31&u)<<12|f<<6|c}else if(240==(248&u)){var l=(7&u)<<18|(f=63&r[e++])<<12|(c=63&r[e++])<<6|63&r[e++];l>65535&&(l-=65536,n[o++]=l>>>10&1023|55296,l=56320|1023&l),n[o++]=l}}else n[o++]=u}}};r="function"==typeof TextDecoder?new TextDecoder:e,q.utf8Decoder=r;var t,n={decode:function(r){var e=new Uint16Array(r.buffer,r.byteOffset,r.byteLength/2);if(e.length<=4096)return String.fromCharCode.apply(null,e);for(var t=[],n=0,a=0;n<e.length;n+=a)a=Math.min(4096,e.length-n),t.push(String.fromCharCode.apply(null,e.subarray(n,n+a)));return t.join("")}};t="function"==typeof TextDecoder?new TextDecoder("utf-16le"):n,q.utf16Decoder=t},lengthBytesUTF8:function(r){for(var e,t=0,n=0;n<r.length;++n)(e=r.charCodeAt(n))<=127?t++:e<=2047?t+=2:e>=55296&&e<=57343?(t+=4,++n):t+=3;return t},UTF8ToString:function(r,e){if(!r||!e)return"";r>>>=0;var t=new Uint8Array(a.buffer),n=r;if(-1===e)for(;t[n];)++n;else n=r+(e>>>0);if((e=n-r)<=16){for(var o=r,s="";o<n;){var i=t[o++];if(128&i){var u=63&t[o++];if(192!=(224&i)){var f=63&t[o++];if((i=224==(240&i)?(15&i)<<12|u<<6|f:(7&i)<<18|u<<12|f<<6|63&t[o++])<65536)s+=String.fromCharCode(i);else{var c=i-65536;s+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else s+=String.fromCharCode((31&i)<<6|u)}else s+=String.fromCharCode(i)}return s}return q.utf8Decoder.decode("function"==typeof SharedArrayBuffer&&t.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(t.buffer)?t.slice(r,n):t.subarray(r,n))},stringToUTF8:function(r,e,t){var n=new Uint8Array(a.buffer),o=e;if(!(t>0))return 0;for(var s=o>>>=0,i=o+t-1,u=0;u<r.length;++u){var f=r.charCodeAt(u);if(f>=55296&&f<=57343)f=65536+((1023&f)<<10)|1023&r.charCodeAt(++u);if(f<=127){if(o>=i)break;n[o++]=f}else if(f<=2047){if(o+1>=i)break;n[o++]=192|f>>6,n[o++]=128|63&f}else if(f<=65535){if(o+2>=i)break;n[o++]=224|f>>12,n[o++]=128|f>>6&63,n[o++]=128|63&f}else{if(o+3>=i)break;n[o++]=240|f>>18,n[o++]=128|f>>12&63,n[o++]=128|f>>6&63,n[o++]=128|63&f}}return n[o]=0,o-s},UTF16ToString:function(r,e){if(!r||!e)return"";var t=r>>>=0;if(-1===e){for(var n=t>>1,o=new Uint16Array(a.buffer);o[n];)++n;t=n<<1}else t=r+2*(e>>>0);if((e=t-r)<=32)return String.fromCharCode.apply(null,new Uint16Array(a.buffer,r,e/2));var s=new Uint8Array(a.buffer);return q.utf16Decoder.decode("function"==typeof SharedArrayBuffer&&s.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(s.buffer)?s.slice(r,t):s.subarray(r,t))},stringToUTF16:function(r,e,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var n=e,o=(t-=2)<2*r.length?t/2:r.length,s=new DataView(a.buffer),i=0;i<o;++i){var u=r.charCodeAt(i);s.setInt16(e,u,!0),e+=2}return s.setInt16(e,0,!0),e-n},newString:function(r,e,t,n,o){if(!r)return 1;var s=d.envStore.get(r);s.checkGCAccess();var i=-1===t,u=t>>>0;if(0!==t&&!e)return s.setLastError(1);if(!n)return s.setLastError(1);if(!(i||u<=2147483647))return s.setLastError(1);var f=o(e,i,u),c=d.addToCurrentScope(f).id;return new DataView(a.buffer).setInt32(n,c,!0),s.clearLastError()},newExternalString:function(r,e,t,n,o,s,i,u,f){if(!r)return 1;var c=d.envStore.get(r);c.checkGCAccess();var l=-1===t,v=t>>>0;if(0!==t&&!e)return c.setLastError(1);if(!s)return c.setLastError(1);if(!(l||v<=2147483647))return c.setLastError(1);var p=u(r,e,t,s);if(0===p){if(i)new DataView(a.buffer).setInt8(i,1,!0);n&&c.callFinalizer(n,e,o)}return p}};function H(r,e,t,n,o,s,i){if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!e)return u.setLastError(1);var f=d.handleStore.get(e);if(!f.isTypedArray())return u.setLastError(1);var c,l=f.value,v=new DataView(a.buffer);if(t){var p=void 0;if(l instanceof Int8Array)p=0;else if(l instanceof Uint8Array)p=1;else if(l instanceof Uint8ClampedArray)p=2;else if(l instanceof Int16Array)p=3;else if(l instanceof Uint16Array)p=4;else if(l instanceof Int32Array)p=5;else if(l instanceof Uint32Array)p=6;else if(l instanceof Float32Array)p=7;else if(l instanceof Float64Array)p=8;else if(l instanceof BigInt64Array)p=9;else{if(!(l instanceof BigUint64Array))return u.setLastError(9);p=10}v.setInt32(t,p,!0)}if(n&&v.setUint32(n,l.length,!0),o||s){if(c=l.buffer,o){var g=P.getViewPointer(l,!0).address;v.setInt32(o,g,!0)}if(s){var y=u.ensureHandleId(c);v.setInt32(s,y,!0)}}return i&&v.setUint32(i,l.byteOffset,!0),u.clearLastError()}function Q(r,e,t,n,o,s){if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!e)return i.setLastError(1);var u=d.handleStore.get(e);if(!u.isDataView())return i.setLastError(1);var f,c=u.value,l=new DataView(a.buffer);if(t&&l.setUint32(t,c.byteLength,!0),n||o){if(f=c.buffer,n){var v=P.getViewPointer(c,!0).address;l.setInt32(n,v,!0)}if(o){var p=i.ensureHandleId(f);l.setInt32(o,p,!0)}}return s&&l.setUint32(s,c.byteOffset,!0),i.clearLastError()}var $=Object.freeze({__proto__:null,napi_get_array_length:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if(!o.isArray())return n.setLastError(8);var s=o.value.length>>>0;return new DataView(a.buffer).setUint32(t,s,!0),n.getReturnStatus()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_get_arraybuffer_info:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!e)return o.setLastError(1);var s=d.handleStore.get(e);if(!s.isArrayBuffer())return o.setLastError(1);var i=new DataView(a.buffer);if(t){var u=P.getArrayBufferPointer(s.value,!0).address;i.setInt32(t,u,!0)}return n&&i.setUint32(n,s.value.byteLength,!0),o.clearLastError()},napi_get_buffer_info:function(r,e,t,n){if(!r)return 1;var a=d.envStore.get(r);if(a.checkGCAccess(),!e)return a.setLastError(1);var o=d.handleStore.get(e);return o.isBuffer(d.feature.Buffer)?o.isDataView()?Q(r,e,n,t,0,0):H(r,e,0,n,t,0,0):a.setLastError(1)},napi_get_dataview_info:Q,napi_get_date_value:function(r,e,t){var n;if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!e)return o.setLastError(1);if(!t)return o.setLastError(1);var s=d.handleStore.get(e);return s.isDate()?(n=s.value.valueOf(),new DataView(a.buffer).setFloat64(t,n,!0),o.getReturnStatus()):o.setLastError(1)}catch(r){return o.tryCatch.setError(r),o.setLastError(10)}},napi_get_prototype:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if(null==o.value)throw new TypeError("Cannot convert undefined or null to object");var s=void 0;try{s=o.isObject()||o.isFunction()?o.value:Object(o.value)}catch(r){return n.setLastError(2)}var i=n.ensureHandleId(Object.getPrototypeOf(s));return new DataView(a.buffer).setInt32(t,i,!0),n.getReturnStatus()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_get_typedarray_info:H,napi_get_value_bigint_int64:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!d.feature.supportBigInt)return o.setLastError(9);if(!e)return o.setLastError(1);if(!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(e).value;if("bigint"!=typeof s)return o.setLastError(6);var i=new DataView(a.buffer);s>=BigInt(-1)*(BigInt(1)<<BigInt(63))&&s<BigInt(1)<<BigInt(63)?i.setInt8(n,1,!0):(i.setInt8(n,0,!0),(s&=(BigInt(1)<<BigInt(64))-BigInt(1))>=BigInt(1)<<BigInt(63)&&(s-=BigInt(1)<<BigInt(64)));var u=Number(s&BigInt(4294967295)),f=Number(s>>BigInt(32));return i.setInt32(t,u,!0),i.setInt32(t+4,f,!0),o.clearLastError()},napi_get_value_bigint_uint64:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!d.feature.supportBigInt)return o.setLastError(9);if(!e)return o.setLastError(1);if(!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(e).value;if("bigint"!=typeof s)return o.setLastError(6);var i=new DataView(a.buffer);s>=BigInt(0)&&s<BigInt(1)<<BigInt(64)?i.setInt8(n,1,!0):(i.setInt8(n,0,!0),s&=(BigInt(1)<<BigInt(64))-BigInt(1));var u=Number(s&BigInt(4294967295)),f=Number(s>>BigInt(32));return i.setUint32(t,u,!0),i.setUint32(t+4,f,!0),o.clearLastError()},napi_get_value_bigint_words:function(r,e,t,n,o){if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!d.feature.supportBigInt)return s.setLastError(9);if(!e)return s.setLastError(1);if(!n)return s.setLastError(1);var i=d.handleStore.get(e);if(!i.isBigInt())return s.setLastError(17);for(var u=i.value<BigInt(0),f=new DataView(a.buffer),c=f.getUint32(n,!0),l=0,v=u?i.value*BigInt(-1):i.value;v!==BigInt(0);)l++,v>>=BigInt(64);if(v=u?i.value*BigInt(-1):i.value,t||o){if(!t)return s.setLastError(1);if(!o)return s.setLastError(1);for(var p=[];v!==BigInt(0);){var g=v&(BigInt(1)<<BigInt(64))-BigInt(1);p.push(g),v>>=BigInt(64)}for(var y=Math.min(c,p.length),h=0;h<y;h++){var E=Number(p[h]&BigInt(4294967295)),_=Number(p[h]>>BigInt(32));f.setUint32(o+8*h,E,!0),f.setUint32(o+(8*h+4),_,!0)}f.setInt32(t,u?1:0,!0),f.setUint32(n,y,!0)}else c=l,f.setUint32(n,c,!0);return s.clearLastError()},napi_get_value_bool:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if("boolean"!=typeof o.value)return n.setLastError(7);var s=o.value?1:0;return new DataView(a.buffer).setInt8(t,s,!0),n.clearLastError()},napi_get_value_double:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if("number"!=typeof o.value)return n.setLastError(6);var s=o.value;return new DataView(a.buffer).setFloat64(t,s,!0),n.clearLastError()},napi_get_value_external:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if(!o.isExternal())return n.setLastError(1);var s=o.data();return new DataView(a.buffer).setInt32(t,s,!0),n.clearLastError()},napi_get_value_int32:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if("number"!=typeof o.value)return n.setLastError(6);var s=new Int32Array([o.value])[0];return new DataView(a.buffer).setInt32(t,s,!0),n.clearLastError()},napi_get_value_int64:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if("number"!=typeof o.value)return n.setLastError(6);var s=o.value,i=new DataView(a.buffer);return s===Number.POSITIVE_INFINITY||s===Number.NEGATIVE_INFINITY||isNaN(s)?(i.setInt32(t,0,!0),i.setInt32(t+4,0,!0)):s<-0x8000000000000000?(i.setInt32(t,0,!0),i.setInt32(t+4,2147483648,!0)):s>=0x8000000000000000?(i.setUint32(t,4294967295,!0),i.setUint32(t+4,2147483647,!0)):A(t,Math.trunc(s)),n.clearLastError()},napi_get_value_string_latin1:function(r,e,t,n,o){if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!e)return s.setLastError(1);n>>>=0;var i=d.handleStore.get(e);if("string"!=typeof i.value)return s.setLastError(3);var u=new DataView(a.buffer);if(t)if(0!==n){for(var f=0,c=void 0,l=0;l<n-1;++l)c=255&i.value.charCodeAt(l),u.setUint8(t+l,c,!0),f++;u.setUint8(t+f,0,!0),o&&u.setUint32(o,f,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return s.setLastError(1);u.setUint32(o,i.value.length,!0)}return s.clearLastError()},napi_get_value_string_utf16:function(r,e,t,n,o){if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!e)return s.setLastError(1);n>>>=0;var i=d.handleStore.get(e);if("string"!=typeof i.value)return s.setLastError(3);var u=new DataView(a.buffer);if(t)if(0!==n){var f=q.stringToUTF16(i.value,t,2*n);o&&u.setUint32(o,f/2,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return s.setLastError(1);u.setUint32(o,i.value.length,!0)}return s.clearLastError()},napi_get_value_string_utf8:function(r,e,t,n,o){if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!e)return s.setLastError(1);n>>>=0;var i=d.handleStore.get(e);if("string"!=typeof i.value)return s.setLastError(3);var u=new DataView(a.buffer);if(t)if(0!==n){var f=q.stringToUTF8(i.value,t,n);o&&u.setUint32(o,f,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return s.setLastError(1);var c=q.lengthBytesUTF8(i.value);u.setUint32(o,c,!0)}return s.clearLastError()},napi_get_value_uint32:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e);if("number"!=typeof o.value)return n.setLastError(6);var s=new Uint32Array([o.value])[0];return new DataView(a.buffer).setUint32(t,s,!0),n.clearLastError()}});function Y(r,e,t,n){return q.newString(r,e,t,n,(function(r,e,t){var n="",o=0,s=new DataView(a.buffer);if(e)for(;;){if(!(i=s.getUint8(r,!0)))break;n+=String.fromCharCode(i),r++}else for(;o<t;){var i;if(!(i=s.getUint8(r,!0)))break;n+=String.fromCharCode(i),o++,r++}return n}))}function X(r,e,t,n){return q.newString(r,e,t,n,(function(r){return q.UTF16ToString(r,t)}))}function Z(r,e,t,n){return q.newString(r,e,t,n,(function(r){return q.UTF8ToString(r,t)}))}var K=Object.freeze({__proto__:null,napi_create_bigint_int64:function(r,e,t,n){if(!r)return 1;var o,s=d.envStore.get(r);if(s.checkGCAccess(),!d.feature.supportBigInt)return s.setLastError(9);if(!t)return s.setLastError(1);o=e;var i=d.addToCurrentScope(o).id;return new DataView(a.buffer).setInt32(t,i,!0),s.clearLastError()},napi_create_bigint_uint64:function(r,e,t,n){if(!r)return 1;var o,s=d.envStore.get(r);if(s.checkGCAccess(),!d.feature.supportBigInt)return s.setLastError(9);if(!t)return s.setLastError(1);o=e&(BigInt(1)<<BigInt(64))-BigInt(1);var i=d.addToCurrentScope(o).id;return new DataView(a.buffer).setInt32(t,i,!0),s.clearLastError()},napi_create_bigint_words:function(r,e,t,n,o){var s,i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!d.feature.supportBigInt)return u.setLastError(9);if(!o)return u.setLastError(1);if((t>>>=0)>2147483647)return u.setLastError(1);if(t>16384)throw new RangeError("Maximum BigInt size exceeded");var f=BigInt(0),c=new DataView(a.buffer);for(i=0;i<t;i++){var l=c.getUint32(n+8*i,!0),v=c.getUint32(n+(8*i+4),!0);f+=(BigInt(l)|BigInt(v)<<BigInt(32))<<BigInt(64*i)}return f*=BigInt(e)%BigInt(2)===BigInt(0)?BigInt(1):BigInt(-1),s=d.addToCurrentScope(f).id,c.setInt32(o,s,!0),u.getReturnStatus()}catch(r){return u.tryCatch.setError(r),u.setLastError(10)}},napi_create_double:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!t)return n.setLastError(1);var o=d.addToCurrentScope(e).id;return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()},napi_create_int32:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!t)return n.setLastError(1);var o=d.addToCurrentScope(e).id;return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()},napi_create_int64:function(r,e,t,n){if(!r)return 1;var o,s=d.envStore.get(r);if(s.checkGCAccess(),!t)return s.setLastError(1);o=Number(e);var i=d.addToCurrentScope(o).id;return new DataView(a.buffer).setInt32(t,i,!0),s.clearLastError()},napi_create_string_latin1:Y,napi_create_string_utf16:X,napi_create_string_utf8:Z,napi_create_uint32:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!t)return n.setLastError(1);var o=d.addToCurrentScope(e>>>0).id;return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()},node_api_create_external_string_latin1:function(r,e,t,n,a,o,s){return q.newExternalString(r,e,t,n,a,o,s,Y,void 0)},node_api_create_external_string_utf16:function(r,e,t,n,a,o,s){return q.newExternalString(r,e,t,n,a,o,s,X,void 0)},node_api_create_property_key_latin1:function(r,e,t,n){return Y(r,e,t,n)},node_api_create_property_key_utf16:function(r,e,t,n){return X(r,e,t,n)},node_api_create_property_key_utf8:function(r,e,t,n){return Z(r,e,t,n)}});function rr(r,e,t,n,a){var s,i=e&&t?q.UTF8ToString(e,t):"",u=o.get(n),f=function(r){return u(r.id,r.ctx.scopeStore.currentScope.id)},c=function(r,e){return function(){var t=r.ctx.openScope(r),n=t.callbackInfo;n.data=a,n.args=arguments,n.thiz=this,n.fn=s;try{var o=r.callIntoModule(e);return o?r.ctx.handleStore.get(o).value:void 0}finally{n.data=0,n.args=void 0,n.thiz=void 0,n.fn=void 0,r.ctx.closeScope(r,t)}}};if(""===i)return{status:0,f:s=c(r,f)};if(!/^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(i))return{status:1,f:void 0};if(d.feature.supportNewFunction){var l=c(r,f);try{s=new Function("_","return function "+i+'(){"use strict";return _.apply(this,arguments);};')(l)}catch(e){s=c(r,f),d.feature.canSetFunctionName&&Object.defineProperty(s,"name",{value:i})}}else s=c(r,f),d.feature.canSetFunctionName&&Object.defineProperty(s,"name",{value:i});return{status:0,f:s}}function er(r,e,t,n,a,o,s,i,u){if(a||o){var f=void 0,c=void 0;a&&(f=rr(r,0,0,a,u).f),o&&(c=rr(r,0,0,o,u).f);var l={configurable:!!(4&i),enumerable:!!(2&i),get:f,set:c};Object.defineProperty(e,t,l)}else if(n){l={configurable:!!(4&i),enumerable:!!(2&i),writable:!!(1&i),value:rr(r,0,0,n,u).f};Object.defineProperty(e,t,l)}else{l={configurable:!!(4&i),enumerable:!!(2&i),writable:!!(1&i),value:d.handleStore.get(s).value};Object.defineProperty(e,t,l)}}function tr(r){var e=d.handleStore.get(r);return e.isObject()||e.isFunction()?(void 0!==P&&ArrayBuffer.isView(e.value)&&P.wasmMemoryViewTable.has(e.value)&&(e=d.addToCurrentScope(P.wasmMemoryViewTable.get(e.value))),{status:0,handle:e}):{status:1}}function nr(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!e)return s.setLastError(1);if(0===n&&!t)return s.setLastError(1);var i=d.handleStore.get(e);if(!i.isObject()&&!i.isFunction())return s.setLastError(1);var u=s.getObjectBinding(i.value),f=u.wrapped,c=d.refStore.get(f);if(!c)return s.setLastError(1);if(t)o=c.data(),new DataView(a.buffer).setInt32(t,o,!0);return 1===n&&(u.wrapped=0,1===c.ownership()?c.resetFinalizer():c.dispose()),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}}function ar(r,e,t,n,o,s){if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!d.feature.supportFinalizer)return i.setLastError(9);if(!e)return i.setLastError(1);if(!n)return i.setLastError(1);var u=tr(e);if(0!==u.status)return i.setLastError(u.status);var f=u.handle,c=s?1:0,l=d.createReferenceWithFinalizer(i,f.id,0,c,n,t,o);if(s){var v=l.id;new DataView(a.buffer).setInt32(s,v,!0)}return i.clearLastError()}var or=Object.freeze({__proto__:null,napi_add_finalizer:ar,napi_check_object_type_tag:function(r,e,t,n){var o=!0;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!e)return s.setLastError(s.tryCatch.hasCaught()?10:1);var i=d.handleStore.get(e);if(!i.isObject()&&!i.isFunction())return s.setLastError(s.tryCatch.hasCaught()?10:2);if(!t)return s.setLastError(s.tryCatch.hasCaught()?10:1);if(!n)return s.setLastError(s.tryCatch.hasCaught()?10:1);var u=s.getObjectBinding(i.value);if(null!==u.tag){var f=u.tag,c=new Uint32Array(a.buffer,t,4);o=f[0]===c[0]&&f[1]===c[1]&&f[2]===c[2]&&f[3]===c[3]}else o=!1;return new DataView(a.buffer).setInt8(n,o?1:0,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_define_class:function(r,e,t,n,o,s,i,u){var f,c,l;if(!r)return 1;var v=d.envStore.get(r);if(v.checkGCAccess(),!v.tryCatch.isEmpty())return v.setLastError(10);if(!v.canCallIntoJs())return v.setLastError(v.moduleApiVersion>=10?23:10);v.clearLastError();try{if(!u)return v.setLastError(1);if(!n)return v.setLastError(1);if((s>>>=0)>0&&!i)return v.setLastError(1);if(t<-1||t>2147483647||!e)return v.setLastError(1);var p=rr(v,e,t,n,o);if(0!==p.status)return v.setLastError(p.status);for(var g=p.f,y=void 0,h=new DataView(a.buffer),E=0;E<s;E++){f=i+32*E;var _=h.getInt32(f,!0),w=h.getInt32(f+4,!0),L=h.getInt32(f+8,!0),m=h.getInt32(f+12,!0),b=h.getInt32(f+16,!0),S=h.getInt32(f+20,!0);l=h.getInt32(f+24,!0);var C=h.getInt32(f+28,!0);if(_)y=q.UTF8ToString(_,-1);else{if(!w)return v.setLastError(4);if("string"!=typeof(y=d.handleStore.get(w).value)&&"symbol"!=typeof y)return v.setLastError(4)}1024&l?er(v,g,y,L,m,b,S,l,C):er(v,g.prototype,y,L,m,b,S,l,C)}return c=d.addToCurrentScope(g).id,h.setInt32(u,c,!0),v.getReturnStatus()}catch(r){return v.tryCatch.setError(r),v.setLastError(10)}},napi_remove_wrap:function(r,e,t){return nr(r,e,t,1)},napi_type_tag_object:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!e)return n.setLastError(n.tryCatch.hasCaught()?10:1);var o=d.handleStore.get(e);if(!o.isObject()&&!o.isFunction())return n.setLastError(n.tryCatch.hasCaught()?10:2);if(!t)return n.setLastError(n.tryCatch.hasCaught()?10:1);var s=n.getObjectBinding(o.value);if(null!==s.tag)return n.setLastError(n.tryCatch.hasCaught()?10:1);var i=new Uint8Array(16);return i.set(new Uint8Array(a.buffer,t,16)),s.tag=new Uint32Array(i.buffer),n.getReturnStatus()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_unwrap:function(r,e,t){return nr(r,e,t,0)},napi_wrap:function(r,e,t,n,o,s){return function(r,e,t,n,o,s){var i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!d.feature.supportFinalizer){if(n)throw d.createNotSupportWeakRefError("napi_wrap",'Parameter "finalize_cb" must be 0(NULL)');if(s)throw d.createNotSupportWeakRefError("napi_wrap",'Parameter "result" must be 0(NULL)')}if(!e)return u.setLastError(1);var f=tr(e);if(0!==f.status)return u.setLastError(f.status);var c=f.handle;if(0!==u.getObjectBinding(c.value).wrapped)return u.setLastError(1);var l=void 0;if(s){if(!n)return u.setLastError(1);i=(l=d.createReferenceWithFinalizer(u,c.id,0,1,n,t,o)).id,new DataView(a.buffer).setInt32(s,i,!0)}else l=n?d.createReferenceWithFinalizer(u,c.id,0,0,n,t,o):d.createReferenceWithData(u,c.id,0,0,t);return u.getObjectBinding(c.value).wrapped=l.id,u.getReturnStatus()}catch(r){return u.tryCatch.setError(r),u.setLastError(10)}}(r,e,t,n,o,s)},node_api_post_finalizer:function(r,e,t,n){if(!r)return 1;var a=d.envStore.get(r);return a.enqueueFinalizer(d.createTrackedFinalizer(a,e,t,n)),a.clearLastError()}});function sr(r,e,t,n,o,s,i){var u;if(!r)return 1;var f=d.envStore.get(r);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!i)return f.setLastError(1);if(n>>>=0,t||(n=0),n>2147483647)throw new RangeError("Cannot create a memory view larger than 2147483647 bytes");if(t+n>a.buffer.byteLength)throw new RangeError("Memory out of range");if(!d.feature.supportFinalizer&&o)throw d.createNotSupportWeakRefError("emnapi_create_memory_view",'Parameter "finalize_cb" must be 0(NULL)');var c=void 0;switch(e){case 0:c={Ctor:Int8Array,address:t,length:n,ownership:1,runtimeAllocated:0};break;case 1:c={Ctor:Uint8Array,address:t,length:n,ownership:1,runtimeAllocated:0};break;case 2:c={Ctor:Uint8ClampedArray,address:t,length:n,ownership:1,runtimeAllocated:0};break;case 3:c={Ctor:Int16Array,address:t,length:n>>1,ownership:1,runtimeAllocated:0};break;case 4:c={Ctor:Uint16Array,address:t,length:n>>1,ownership:1,runtimeAllocated:0};break;case 5:c={Ctor:Int32Array,address:t,length:n>>2,ownership:1,runtimeAllocated:0};break;case 6:c={Ctor:Uint32Array,address:t,length:n>>2,ownership:1,runtimeAllocated:0};break;case 7:c={Ctor:Float32Array,address:t,length:n>>2,ownership:1,runtimeAllocated:0};break;case 8:c={Ctor:Float64Array,address:t,length:n>>3,ownership:1,runtimeAllocated:0};break;case 9:c={Ctor:BigInt64Array,address:t,length:n>>3,ownership:1,runtimeAllocated:0};break;case 10:c={Ctor:BigUint64Array,address:t,length:n>>3,ownership:1,runtimeAllocated:0};break;case-1:c={Ctor:DataView,address:t,length:n,ownership:1,runtimeAllocated:0};break;case-2:if(!d.feature.Buffer)throw d.createNotSupportBufferError("emnapi_create_memory_view","");c={Ctor:d.feature.Buffer,address:t,length:n,ownership:1,runtimeAllocated:0};break;default:return f.setLastError(1)}var l=c.Ctor,v=-2===e?d.feature.Buffer.from(a.buffer,c.address,c.length):new l(a.buffer,c.address,c.length),p=d.addToCurrentScope(v);if(P.wasmMemoryViewTable.set(v,c),o){var g=ar(r,p.id,t,o,s,0);if(10===g){var y=f.tryCatch.extractException();throw f.clearLastError(),y}if(0!==g)return f.setLastError(g)}return u=p.id,new DataView(a.buffer).setInt32(i,u,!0),f.getReturnStatus()}catch(y){return f.tryCatch.setError(y),f.setLastError(10)}}function ir(r,e,t,n){var o;if(t=null!=t?t:0,t>>>=0,e instanceof ArrayBuffer){if(!(i=P.getArrayBufferPointer(e,!1).address))throw new Error("Unknown ArrayBuffer address");if("number"==typeof n&&-1!==n||(n=e.byteLength-t),0===(n>>>=0))return e;o=new Uint8Array(e,t,n);var s=new Uint8Array(a.buffer);return r?s.set(o,i):o.set(s.subarray(i,i+n)),e}if(ArrayBuffer.isView(e)){var i,u=P.getViewPointer(e,!1),f=u.view;if(!(i=u.address))throw new Error("Unknown ArrayBuffer address");if("number"==typeof n&&-1!==n||(n=f.byteLength-t),0===(n>>>=0))return f;o=new Uint8Array(f.buffer,f.byteOffset+t,n);s=new Uint8Array(a.buffer);return r?s.set(o,i):o.set(s.subarray(i,i+n)),f}throw new TypeError("emnapiSyncMemory expect ArrayBuffer or ArrayBufferView as first parameter")}function ur(r){var e,t=r instanceof ArrayBuffer,n=r instanceof DataView,a=ArrayBuffer.isView(r)&&!n;if(!t&&!a&&!n)throw new TypeError("emnapiGetMemoryAddress expect ArrayBuffer or ArrayBufferView as first parameter");return{address:(e=t?P.getArrayBufferPointer(r,!1):P.getViewPointer(r,!1)).address,ownership:e.ownership,runtimeAllocated:e.runtimeAllocated}}var fr=Object.freeze({__proto__:null,$emnapiGetMemoryAddress:ur,$emnapiSyncMemory:ir,emnapi_create_memory_view:sr,emnapi_get_memory_address:function(r,e,t,n,o){var s,i,u,f;if(!r)return 1;var c=d.envStore.get(r);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!e)return c.setLastError(1);if(!t&&!n&&!o)return c.setLastError(1);s=(f=ur(c.ctx.handleStore.get(e).value)).address;var l=new DataView(a.buffer);return t&&l.setInt32(t,s,!0),n&&(u=f.ownership,l.setInt32(n,u,!0)),o&&(i=f.runtimeAllocated,l.setInt8(o,i,!0)),c.getReturnStatus()}catch(r){return c.tryCatch.setError(r),c.setLastError(10)}},emnapi_get_runtime_version:function(r,e){if(!r)return 1;var t,n=d.envStore.get(r);if(!e)return n.setLastError(1);try{t=d.getRuntimeVersions().version}catch(r){return n.setLastError(9)}var o=t.split(".").map((function(r){return Number(r)})),s=new DataView(a.buffer);return s.setUint32(e,o[0],!0),s.setUint32(e+4,o[1],!0),s.setUint32(e+8,o[2],!0),n.clearLastError()},emnapi_is_node_binding_available:function(){return v?1:0},emnapi_is_support_bigint:function(){return d.feature.supportBigInt?1:0},emnapi_is_support_weakref:function(){return d.feature.supportFinalizer?1:0},emnapi_sync_memory:function(r,e,t,n,o){var s;if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);var u=new DataView(a.buffer),f=u.getInt32(t,!0),c=i.ctx.handleStore.get(f);if(!c.isArrayBuffer()&&!c.isTypedArray()&&!c.isDataView())return i.setLastError(1);var l=ir(Boolean(e),c.value,n,o);return c.value!==l&&(s=i.ensureHandleId(l),u.setInt32(t,s,!0)),i.getReturnStatus()}catch(r){return i.tryCatch.setError(r),i.setLastError(10)}}});function cr(r,e){r>>>=0;var t=new ArrayBuffer(r);if(e){var n=P.getArrayBufferPointer(t,!0).address;new DataView(a.buffer).setInt32(e,n,!0)}return t}var lr=Object.freeze({__proto__:null,napi_create_array:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=d.addToCurrentScope([]).id;return new DataView(a.buffer).setInt32(e,n,!0),t.clearLastError()},napi_create_array_with_length:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!t)return n.setLastError(1);e>>>=0;var o=d.addToCurrentScope(new Array(e)).id;return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()},napi_create_arraybuffer:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!n)return s.setLastError(1);var i=cr(e,t);return o=d.addToCurrentScope(i).id,new DataView(a.buffer).setInt32(n,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_create_buffer:function(r,e,t,n){var o,i,u;if(!r)return 1;var f=d.envStore.get(r);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!n)return f.setLastError(1);var c=d.feature.Buffer;if(!c)throw d.createNotSupportBufferError("napi_create_buffer","");var l=void 0;e>>>=0;var v=new DataView(a.buffer);if(t&&0!==e){if(!(u=s(e)))throw new Error("Out of memory");new Uint8Array(a.buffer).subarray(u,u+e).fill(0);var p=c.from(a.buffer,u,e),g={Ctor:c,address:u,length:e,ownership:P.registry?0:1,runtimeAllocated:1};P.wasmMemoryViewTable.set(p,g),null===(o=P.registry)||void 0===o||o.register(g,u),i=d.addToCurrentScope(p).id,v.setInt32(n,i,!0),v.setInt32(t,u,!0)}else l=c.alloc(e),i=d.addToCurrentScope(l).id,v.setInt32(n,i,!0);return f.getReturnStatus()}catch(r){return f.tryCatch.setError(r),f.setLastError(10)}},napi_create_buffer_copy:function(r,e,t,n,o){var s;if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!o)return i.setLastError(1);var u=d.feature.Buffer;if(!u)throw d.createNotSupportBufferError("napi_create_buffer_copy","");var f=cr(e,n),c=u.from(f);return c.set(new Uint8Array(a.buffer).subarray(t,t+e)),s=d.addToCurrentScope(c).id,new DataView(a.buffer).setInt32(o,s,!0),i.getReturnStatus()}catch(r){return i.tryCatch.setError(r),i.setLastError(10)}},napi_create_dataview:function(r,e,t,n,o){var s;if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);if(!o)return i.setLastError(1);e>>>=0,n>>>=0;var u=d.handleStore.get(t);if(!u.isArrayBuffer())return i.setLastError(1);var f=u.value;if(e+n>f.byteLength){var c=new RangeError("byte_offset + byte_length should be less than or equal to the size in bytes of the array passed in");throw c.code="ERR_NAPI_INVALID_DATAVIEW_ARGS",c}var l=new DataView(f,n,e);return f===a.buffer&&(P.wasmMemoryViewTable.has(l)||P.wasmMemoryViewTable.set(l,{Ctor:DataView,address:n,length:e,ownership:1,runtimeAllocated:0})),s=d.addToCurrentScope(l).id,new DataView(a.buffer).setInt32(o,s,!0),i.getReturnStatus()}catch(c){return i.tryCatch.setError(c),i.setLastError(10)}},napi_create_date:function(r,e,t){var n;if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{return t?(n=d.addToCurrentScope(new Date(e)).id,new DataView(a.buffer).setInt32(t,n,!0),o.getReturnStatus()):o.setLastError(1)}catch(r){return o.tryCatch.setError(r),o.setLastError(10)}},napi_create_external:function(r,e,t,n,o){var s;if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!o)return i.setLastError(1);if(!d.feature.supportFinalizer&&t)throw d.createNotSupportWeakRefError("napi_create_external",'Parameter "finalize_cb" must be 0(NULL)');var u=d.getCurrentScope().addExternal(e);return t&&d.createReferenceWithFinalizer(i,u.id,0,0,t,e,n),s=u.id,new DataView(a.buffer).setInt32(o,s,!0),i.clearLastError()}catch(r){return i.tryCatch.setError(r),i.setLastError(10)}},napi_create_external_arraybuffer:function(r,e,t,n,o,s){var i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!s)return u.setLastError(1);if(t>>>=0,e||(t=0),e+t>a.buffer.byteLength)throw new RangeError("Memory out of range");if(!d.feature.supportFinalizer&&n)throw d.createNotSupportWeakRefError("napi_create_external_arraybuffer",'Parameter "finalize_cb" must be 0(NULL)');var f=new ArrayBuffer(t);if(0===t)try{(new(0,d.feature.MessageChannel)).port1.postMessage(f,[f])}catch(r){}else new Uint8Array(f).set(new Uint8Array(a.buffer).subarray(e,e+t)),P.table.set(f,{address:e,ownership:1,runtimeAllocated:0});var c=d.addToCurrentScope(f);if(n){var l=ar(r,c.id,e,n,o,0);if(10===l){var v=u.tryCatch.extractException();throw u.clearLastError(),v}if(0!==l)return u.setLastError(l)}return i=c.id,new DataView(a.buffer).setInt32(s,i,!0),u.getReturnStatus()}catch(v){return u.tryCatch.setError(v),u.setLastError(10)}},napi_create_external_buffer:function(r,e,t,n,a,o){return sr(r,-2,t,e,n,a,o)},napi_create_object:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=d.addToCurrentScope({}).id;return new DataView(a.buffer).setInt32(e,n,!0),t.clearLastError()},napi_create_symbol:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!t)return n.setLastError(1);var o=new DataView(a.buffer);if(e){var s=d.handleStore.get(e).value;if("string"!=typeof s)return n.setLastError(3);var i=d.addToCurrentScope(Symbol(s)).id;o.setInt32(t,i,!0)}else{var u=d.addToCurrentScope(Symbol()).id;o.setInt32(t,u,!0)}return n.clearLastError()},napi_create_typedarray:function(r,e,t,n,o,s){var i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!n)return u.setLastError(1);if(!s)return u.setLastError(1);var f=d.handleStore.get(n);if(!f.isArrayBuffer())return u.setLastError(1);var c=f.value,l=function(r,e,t,n,o,u){var f,c;if((o>>>=0,u>>>=0,t>1)&&o%t!=0)return(c=new RangeError("start offset of ".concat(null!==(f=e.name)&&void 0!==f?f:""," should be a multiple of ").concat(t))).code="ERR_NAPI_INVALID_TYPEDARRAY_ALIGNMENT",r.tryCatch.setError(c),r.setLastError(9);if(u*t+o>n.byteLength)return(c=new RangeError("Invalid typed array length")).code="ERR_NAPI_INVALID_TYPEDARRAY_LENGTH",r.tryCatch.setError(c),r.setLastError(9);var l=new e(n,o,u);return n===a.buffer&&(P.wasmMemoryViewTable.has(l)||P.wasmMemoryViewTable.set(l,{Ctor:e,address:o,length:u,ownership:1,runtimeAllocated:0})),i=d.addToCurrentScope(l).id,new DataView(a.buffer).setInt32(s,i,!0),r.getReturnStatus()};switch(e){case 0:return l(u,Int8Array,1,c,o,t);case 1:return l(u,Uint8Array,1,c,o,t);case 2:return l(u,Uint8ClampedArray,1,c,o,t);case 3:return l(u,Int16Array,2,c,o,t);case 4:return l(u,Uint16Array,2,c,o,t);case 5:return l(u,Int32Array,4,c,o,t);case 6:return l(u,Uint32Array,4,c,o,t);case 7:return l(u,Float32Array,4,c,o,t);case 8:return l(u,Float64Array,8,c,o,t);case 9:return l(u,BigInt64Array,8,c,o,t);case 10:return l(u,BigUint64Array,8,c,o,t);default:return u.setLastError(1)}}catch(r){return u.tryCatch.setError(r),u.setLastError(10)}},node_api_create_buffer_from_arraybuffer:function(r,e,t,n,o){var s;if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!e)return i.setLastError(1);if(!o)return i.setLastError(1);t>>>=0,n>>>=0;var u=d.handleStore.get(e);if(!u.isArrayBuffer())return i.setLastError(1);var f=u.value;if(n+t>f.byteLength){var c=new RangeError("The byte offset + length is out of range");throw c.code="ERR_OUT_OF_RANGE",c}var l=d.feature.Buffer;if(!l)throw d.createNotSupportBufferError("node_api_create_buffer_from_arraybuffer","");var v=l.from(f,t,n);return f===a.buffer&&(P.wasmMemoryViewTable.has(v)||P.wasmMemoryViewTable.set(v,{Ctor:l,address:t,length:n,ownership:1,runtimeAllocated:0})),s=d.addToCurrentScope(v).id,new DataView(a.buffer).setInt32(o,s,!0),i.getReturnStatus()}catch(c){return i.tryCatch.setError(c),i.setLastError(10)}},node_api_symbol_for:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!n)return o.setLastError(1);var s=-1===t,i=t>>>0;if(0!==t&&!e)return o.setLastError(1);if(!(s||i<=2147483647))return o.setLastError(1);var u=q.UTF8ToString(e,t),f=d.addToCurrentScope(Symbol.for(u)).id;return new DataView(a.buffer).setInt32(n,f,!0),o.clearLastError()}});var dr=Object.freeze({__proto__:null,napi_get_boolean:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!t)return n.setLastError(1);var o=0===e?3:4;return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()},napi_get_global:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return t.checkGCAccess(),e?(new DataView(a.buffer).setInt32(e,5,!0),t.clearLastError()):t.setLastError(1)},napi_get_null:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return t.checkGCAccess(),e?(new DataView(a.buffer).setInt32(e,2,!0),t.clearLastError()):t.setLastError(1)},napi_get_undefined:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return t.checkGCAccess(),e?(new DataView(a.buffer).setInt32(e,1,!0),t.clearLastError()):t.setLastError(1)}});var vr=Object.freeze({__proto__:null,napi_get_instance_data:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(!e)return t.setLastError(1);var n=t.getInstanceData();return new DataView(a.buffer).setInt32(e,n,!0),t.clearLastError()},napi_set_instance_data:function(r,e,t,n){if(!r)return 1;var a=d.envStore.get(r);return a.setInstanceData(e,t,n),a.clearLastError()}});var pr=Object.freeze({__proto__:null,_emnapi_get_last_error_info:function(r,e,t,n){var o=d.envStore.get(r).lastError,s=o.errorCode,i=o.engineErrorCode>>>0,u=o.engineReserved,f=new DataView(a.buffer);f.setInt32(e,s,!0),f.setUint32(t,i,!0),f.setInt32(n,u,!0)},napi_create_error:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new Error(s);if(e){var u=d.handleStore.get(e).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=d.addToCurrentScope(i).id;return new DataView(a.buffer).setInt32(n,f,!0),o.clearLastError()},napi_create_range_error:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new RangeError(s);if(e){var u=d.handleStore.get(e).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=d.addToCurrentScope(i).id;return new DataView(a.buffer).setInt32(n,f,!0),o.clearLastError()},napi_create_type_error:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new TypeError(s);if(e){var u=d.handleStore.get(e).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=d.addToCurrentScope(i).id;return new DataView(a.buffer).setInt32(n,f,!0),o.clearLastError()},napi_fatal_error:function(r,e,t,n){var a=q.UTF8ToString(r,e),o=q.UTF8ToString(t,n);v?v.napi.fatalError(a,o):l("FATAL ERROR: "+a+" "+o)},napi_fatal_exception:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!e)return t.setLastError(1);var n=t.ctx.handleStore.get(e);try{t.triggerFatalException(n.value)}catch(r){return t.setLastError(9)}return t.clearLastError()}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_get_and_clear_last_exception:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=new DataView(a.buffer);if(!t.tryCatch.hasCaught())return n.setInt32(e,1,!0),t.clearLastError();var o=t.tryCatch.exception(),s=t.ensureHandleId(o);return n.setInt32(e,s,!0),t.tryCatch.reset(),t.clearLastError()},napi_is_exception_pending:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=t.tryCatch.hasCaught();return new DataView(a.buffer).setInt8(e,n?1:0,!0),t.clearLastError()},napi_throw:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{return e?(t.tryCatch.setError(d.handleStore.get(e).value),t.clearLastError()):t.setLastError(1)}catch(r){return t.tryCatch.setError(r),t.setLastError(10)}},napi_throw_error:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new Error(q.UTF8ToString(t,-1));return e&&(a.code=q.UTF8ToString(e,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_throw_range_error:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new RangeError(q.UTF8ToString(t,-1));return e&&(a.code=q.UTF8ToString(e,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_throw_type_error:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new TypeError(q.UTF8ToString(t,-1));return e&&(a.code=q.UTF8ToString(e,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},node_api_create_syntax_error:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new SyntaxError(s);if(e){var u=d.handleStore.get(e).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=d.addToCurrentScope(i).id;return new DataView(a.buffer).setInt32(n,f,!0),o.clearLastError()},node_api_throw_syntax_error:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new SyntaxError(q.UTF8ToString(t,-1));return e&&(a.code=q.UTF8ToString(e,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}}});var gr=Object.freeze({__proto__:null,napi_call_function:function(r,e,t,n,o,s){var i,u=0;if(!r)return 1;var f=d.envStore.get(r);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!e)return f.setLastError(1);if((n>>>=0)>0&&!o)return f.setLastError(1);var c=d.handleStore.get(e).value;if(!t)return f.setLastError(1);var l=d.handleStore.get(t).value;if("function"!=typeof l)return f.setLastError(1);for(var v=[],p=new DataView(a.buffer);u<n;u++){var g=p.getInt32(o+4*u,!0);v.push(d.handleStore.get(g).value)}var y=l.apply(c,v);return s&&(i=f.ensureHandleId(y),p.setInt32(s,i,!0)),f.clearLastError()}catch(r){return f.tryCatch.setError(r),f.setLastError(10)}},napi_create_function:function(r,e,t,n,o,s){var i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!s)return u.setLastError(1);if(!n)return u.setLastError(1);var f=rr(u,e,t,n,o);if(0!==f.status)return u.setLastError(f.status);var c=f.f;return i=d.addToCurrentScope(c).id,new DataView(a.buffer).setInt32(s,i,!0),u.getReturnStatus()}catch(r){return u.tryCatch.setError(r),u.setLastError(10)}},napi_get_cb_info:function(r,e,t,n,o,s){if(!r)return 1;var i=d.envStore.get(r);if(!e)return i.setLastError(1);var u=d.scopeStore.get(e).callbackInfo,f=new DataView(a.buffer);if(n){if(!t)return i.setLastError(1);for(var c=f.getUint32(t,!0),l=u.args.length,v=c<l?c:l,p=0;p<v;p++){var g=i.ensureHandleId(u.args[p]);f.setInt32(n+4*p,g,!0)}if(p<c)for(;p<c;p++)f.setInt32(n+4*p,1,!0)}if(t&&f.setUint32(t,u.args.length,!0),o){var y=i.ensureHandleId(u.thiz);f.setInt32(o,y,!0)}return s&&f.setInt32(s,u.data,!0),i.clearLastError()},napi_get_new_target:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.scopeStore.get(e).callbackInfo,s=o.thiz,i=o.fn,u=null==s||null==s.constructor?0:s instanceof i?n.ensureHandleId(s.constructor):0;return new DataView(a.buffer).setInt32(t,u,!0),n.clearLastError()},napi_new_instance:function(r,e,t,n,o){var s,i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!e)return u.setLastError(1);if((t>>>=0)>0&&!n)return u.setLastError(1);if(!o)return u.setLastError(1);var f=d.handleStore.get(e).value;if("function"!=typeof f)return u.setLastError(1);var c=void 0,l=new DataView(a.buffer);if(d.feature.supportReflect){var v=Array(t);for(s=0;s<t;s++){var p=l.getInt32(n+4*s,!0);v[s]=d.handleStore.get(p).value}c=Reflect.construct(f,v,f)}else{var g=Array(t+1);for(g[0]=void 0,s=0;s<t;s++){p=l.getInt32(n+4*s,!0);g[s+1]=d.handleStore.get(p).value}c=new(f.bind.apply(f,g))}return o&&(i=u.ensureHandleId(c),l.setInt32(o,i,!0)),u.getReturnStatus()}catch(r){return u.tryCatch.setError(r),u.setLastError(10)}}});var yr=Object.freeze({__proto__:null,_emnapi_env_ref:function(r){d.envStore.get(r).ref()},_emnapi_env_unref:function(r){d.envStore.get(r).unref()},napi_add_env_cleanup_hook:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);return e?(d.addCleanupHook(n,e,t),0):n.setLastError(1)},napi_close_escapable_handle_scope:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return t.checkGCAccess(),e?0===t.openHandleScopes?13:(d.closeScope(t),t.clearLastError()):t.setLastError(1)},napi_close_handle_scope:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return t.checkGCAccess(),e?0===t.openHandleScopes?13:(d.closeScope(t),t.clearLastError()):t.setLastError(1)},napi_create_reference:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!e)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.handleStore.get(e);if(o.moduleApiVersion<10&&!(s.isObject()||s.isFunction()||s.isSymbol()))return o.setLastError(1);var i=d.createReference(o,s.id,t>>>0,1);return new DataView(a.buffer).setInt32(n,i.id,!0),o.clearLastError()},napi_delete_reference:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return e?(d.refStore.get(e).dispose(),t.clearLastError()):t.setLastError(1)},napi_escape_handle:function(r,e,t,n){if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!e)return o.setLastError(1);if(!t)return o.setLastError(1);if(!n)return o.setLastError(1);var s=d.scopeStore.get(e);if(!s.escapeCalled()){var i=s.escape(t),u=i?i.id:0;return new DataView(a.buffer).setInt32(n,u,!0),o.clearLastError()}return o.setLastError(12)},napi_get_reference_value:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.refStore.get(e).get(n);return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()},napi_open_escapable_handle_scope:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=d.openScope(t);return new DataView(a.buffer).setInt32(e,n.id,!0),t.clearLastError()},napi_open_handle_scope:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=d.openScope(t);return new DataView(a.buffer).setInt32(e,n.id,!0),t.clearLastError()},napi_reference_ref:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);var o=d.refStore.get(e).ref();return t&&new DataView(a.buffer).setUint32(t,o,!0),n.clearLastError()},napi_reference_unref:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);var o=d.refStore.get(e);if(0===o.refcount())return n.setLastError(9);var s=o.unref();return t&&new DataView(a.buffer).setUint32(t,s,!0),n.clearLastError()},napi_remove_env_cleanup_hook:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);return e?(d.removeCleanupHook(n,e,t),0):n.setLastError(1)}});var hr=Object.freeze({__proto__:null,_emnapi_get_filename:function(r,e,t){var n=d.envStore.get(r).filename;return e?q.stringToUTF8(n,e,t):q.lengthBytesUTF8(n)}});var Er=Object.freeze({__proto__:null,napi_create_promise:function(r,e,t){var n,o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!e)return s.setLastError(1);if(!t)return s.setLastError(1);var i=new DataView(a.buffer),u=new Promise((function(r,t){var a=d.createDeferred({resolve:r,reject:t});n=a.id,i.setInt32(e,n,!0)}));return o=d.addToCurrentScope(u).id,i.setInt32(t,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_is_promise:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isPromise()?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_reject_deferred:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{return e&&t?(d.deferredStore.get(e).reject(d.handleStore.get(t).value),n.getReturnStatus()):n.setLastError(1)}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_resolve_deferred:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{return e&&t?(d.deferredStore.get(e).resolve(d.handleStore.get(t).value),n.getReturnStatus()):n.setLastError(1)}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}}});function _r(r,e,t,n,o,s){var i;if(!r)return 1;var u=d.envStore.get(r);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!s)return u.setLastError(1);if(!e)return u.setLastError(1);var f=d.handleStore.get(e);if(null==f.value)throw new TypeError("Cannot convert undefined or null to object");var c=void 0;try{c=f.isObject()||f.isFunction()?f.value:Object(f.value)}catch(r){return u.setLastError(2)}if(0!==t&&1!==t)return u.setLastError(1);if(0!==o&&1!==o)return u.setLastError(1);var l=[],v=void 0,p=void 0,g=void 0,y=!0,h=/^(0|[1-9][0-9]*)$/;do{for(v=Object.getOwnPropertyNames(c),p=Object.getOwnPropertySymbols(c),g=0;g<v.length;g++)l.push({name:h.test(v[g])?Number(v[g]):v[g],desc:Object.getOwnPropertyDescriptor(c,v[g]),own:y});for(g=0;g<p.length;g++)l.push({name:p[g],desc:Object.getOwnPropertyDescriptor(c,p[g]),own:y});if(1===t)break;c=Object.getPrototypeOf(c),y=!1}while(c);var E=[],_=function(r,e,t,n){if(-1===r.indexOf(e))if(0===n)r.push(e);else if(1===n){var a="number"==typeof e?String(e):e;"string"==typeof a&&8&t||r.push(a)}};for(g=0;g<l.length;g++){var w=l[g],L=w.name,m=w.desc;if(0===n)_(E,L,n,o);else{if(8&n&&"string"==typeof L)continue;if(16&n&&"symbol"==typeof L)continue;var b=!0;switch(7&n){case 1:b=Boolean(m.writable);break;case 2:b=Boolean(m.enumerable);break;case 3:b=Boolean(m.writable&&m.enumerable);break;case 4:b=Boolean(m.configurable);break;case 5:b=Boolean(m.configurable&&m.writable);break;case 6:b=Boolean(m.configurable&&m.enumerable);break;case 7:b=Boolean(m.configurable&&m.enumerable&&m.writable)}b&&_(E,L,n,o)}}return i=d.addToCurrentScope(E).id,new DataView(a.buffer).setInt32(s,i,!0),u.getReturnStatus()}catch(r){return u.tryCatch.setError(r),u.setLastError(10)}}var wr=Object.freeze({__proto__:null,napi_define_properties:function(r,e,t,n){var o,s;if(!r)return 1;var i=d.envStore.get(r);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if((t>>>=0)>0&&!n)return i.setLastError(1);if(!e)return i.setLastError(1);var u=d.handleStore.get(e),f=u.value;if(!u.isObject()&&!u.isFunction())return i.setLastError(2);for(var c=void 0,l=new DataView(a.buffer),v=0;v<t;v++){o=n+32*v;var p=l.getInt32(o,!0),g=l.getInt32(o+4,!0),y=l.getInt32(o+8,!0),h=l.getInt32(o+12,!0),E=l.getInt32(o+16,!0),_=l.getInt32(o+20,!0);s=l.getInt32(o+24,!0);var w=l.getInt32(o+28,!0);if(p)c=q.UTF8ToString(p,-1);else{if(!g)return i.setLastError(4);if("string"!=typeof(c=d.handleStore.get(g).value)&&"symbol"!=typeof c)return i.setLastError(4)}er(i,f,c,y,h,E,_,s,w)}return i.getReturnStatus()}catch(r){return i.tryCatch.setError(r),i.setLastError(10)}},napi_delete_element:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(!i.isObject()&&!i.isFunction())return s.setLastError(2);if(d.feature.supportReflect)o=Reflect.deleteProperty(i.value,t>>>0);else try{o=delete i.value[t>>>0]}catch(r){o=!1}if(n)new DataView(a.buffer).setInt8(n,o?1:0,!0);return s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_delete_property:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(!i.isObject()&&!i.isFunction())return s.setLastError(2);var u=d.handleStore.get(t).value;if(d.feature.supportReflect)o=Reflect.deleteProperty(i.value,u);else try{o=delete i.value[u]}catch(r){o=!1}if(n)new DataView(a.buffer).setInt8(n,o?1:0,!0);return s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_get_all_property_names:_r,napi_get_element:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}return o=s.ensureHandleId(u[t>>>0]),new DataView(a.buffer).setInt32(n,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_get_named_property:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);if(!t)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}return o=s.ensureHandleId(u[q.UTF8ToString(t,-1)]),new DataView(a.buffer).setInt32(n,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_get_property:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}return o=s.ensureHandleId(u[d.handleStore.get(t).value]),new DataView(a.buffer).setInt32(n,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_get_property_names:function(r,e,t){return _r(r,e,0,18,1,t)},napi_has_element:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}return o=t>>>0 in u?1:0,new DataView(a.buffer).setInt8(n,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_has_named_property:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);if(!t)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}return o=q.UTF8ToString(t,-1)in u,new DataView(a.buffer).setInt8(n,o?1:0,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_has_own_property:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}var f=d.handleStore.get(t).value;return"string"!=typeof f&&"symbol"!=typeof f?s.setLastError(4):(o=Object.prototype.hasOwnProperty.call(u,d.handleStore.get(t).value),new DataView(a.buffer).setInt8(n,o?1:0,!0),s.getReturnStatus())}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_has_property:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!n)return s.setLastError(1);if(!e)return s.setLastError(1);var i=d.handleStore.get(e);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(r){return s.setLastError(2)}return o=d.handleStore.get(t).value in u?1:0,new DataView(a.buffer).setInt8(n,o,!0),s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_object_freeze:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!e)return t.setLastError(1);var n=d.handleStore.get(e),a=n.value;return n.isObject()||n.isFunction()?(Object.freeze(a),t.getReturnStatus()):t.setLastError(2)}catch(r){return t.tryCatch.setError(r),t.setLastError(10)}},napi_object_seal:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!e)return t.setLastError(1);var n=d.handleStore.get(e),a=n.value;return n.isObject()||n.isFunction()?(Object.seal(a),t.getReturnStatus()):t.setLastError(2)}catch(r){return t.tryCatch.setError(r),t.setLastError(10)}},napi_set_element:function(r,e,t,n){if(!r)return 1;var a=d.envStore.get(r);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!n)return a.setLastError(1);if(!e)return a.setLastError(1);var o=d.handleStore.get(e);return o.isObject()||o.isFunction()?(o.value[t>>>0]=d.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(2)}catch(r){return a.tryCatch.setError(r),a.setLastError(10)}},napi_set_named_property:function(r,e,t,n){if(!r)return 1;var a=d.envStore.get(r);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!n)return a.setLastError(1);if(!e)return a.setLastError(1);var o=d.handleStore.get(e);return o.isObject()||o.isFunction()?t?(d.handleStore.get(e).value[q.UTF8ToString(t,-1)]=d.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(1):a.setLastError(2)}catch(r){return a.tryCatch.setError(r),a.setLastError(10)}},napi_set_property:function(r,e,t,n){if(!r)return 1;var a=d.envStore.get(r);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!t)return a.setLastError(1);if(!n)return a.setLastError(1);if(!e)return a.setLastError(1);var o=d.handleStore.get(e);return o.isObject()||o.isFunction()?(o.value[d.handleStore.get(t).value]=d.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(2)}catch(r){return a.tryCatch.setError(r),a.setLastError(10)}}});var Lr=Object.freeze({__proto__:null,napi_run_script:function(r,e,t){var n,o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!e)return s.setLastError(1);if(!t)return s.setLastError(1);var i=d.handleStore.get(e);if(!i.isString())return s.setLastError(3);var u=d.handleStore.get(5).value.eval(i.value);o=s.ensureHandleId(u),new DataView(a.buffer).setInt32(t,o,!0),n=s.getReturnStatus()}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}return n}});var mr=Object.freeze({__proto__:null,napi_coerce_to_bool:function(r,e,t){var n;if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{return e&&t?(n=d.handleStore.get(e).value?4:3,new DataView(a.buffer).setInt32(t,n,!0),o.getReturnStatus()):o.setLastError(1)}catch(r){return o.tryCatch.setError(r),o.setLastError(10)}},napi_coerce_to_number:function(r,e,t){var n;if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!e)return o.setLastError(1);if(!t)return o.setLastError(1);var s=d.handleStore.get(e);if(s.isBigInt())throw new TypeError("Cannot convert a BigInt value to a number");return n=d.addToCurrentScope(Number(s.value)).id,new DataView(a.buffer).setInt32(t,n,!0),o.getReturnStatus()}catch(r){return o.tryCatch.setError(r),o.setLastError(10)}},napi_coerce_to_object:function(r,e,t){var n;if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!e)return o.setLastError(1);if(!t)return o.setLastError(1);var s=d.handleStore.get(e);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");return n=o.ensureHandleId(Object(s.value)),new DataView(a.buffer).setInt32(t,n,!0),o.getReturnStatus()}catch(r){return o.tryCatch.setError(r),o.setLastError(10)}},napi_coerce_to_string:function(r,e,t){var n;if(!r)return 1;var o=d.envStore.get(r);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!e)return o.setLastError(1);if(!t)return o.setLastError(1);var s=d.handleStore.get(e);if(s.isSymbol())throw new TypeError("Cannot convert a Symbol value to a string");return n=d.addToCurrentScope(String(s.value)).id,new DataView(a.buffer).setInt32(t,n,!0),o.getReturnStatus()}catch(r){return o.tryCatch.setError(r),o.setLastError(10)}},napi_detach_arraybuffer:function(r,e){if(!r)return 1;var t=d.envStore.get(r);if(t.checkGCAccess(),!e)return t.setLastError(1);var n=d.handleStore.get(e).value;if(!(n instanceof ArrayBuffer))return"function"==typeof SharedArrayBuffer&&n instanceof SharedArrayBuffer?t.setLastError(20):t.setLastError(19);try{(new(0,d.feature.MessageChannel)).port1.postMessage(n,[n])}catch(r){return t.setLastError(9)}return t.clearLastError()},napi_instanceof:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!e)return s.setLastError(1);if(!n)return s.setLastError(1);if(!t)return s.setLastError(1);var i=new DataView(a.buffer);i.setInt8(n,0,!0);var u=d.handleStore.get(t);return u.isFunction()?(o=d.handleStore.get(e).value instanceof u.value?1:0,i.setInt8(n,o,!0),s.getReturnStatus()):s.setLastError(5)}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_is_array:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isArray()?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_is_arraybuffer:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isArrayBuffer()?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_is_buffer:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isBuffer(d.feature.Buffer)?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_is_dataview:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isDataView()?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_is_date:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isDate()?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_is_detached_arraybuffer:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e),s=new DataView(a.buffer);if(o.isArrayBuffer()&&0===o.value.byteLength)try{new Uint8Array(o.value)}catch(r){return s.setInt8(t,1,!0),n.getReturnStatus()}return s.setInt8(t,0,!0),n.getReturnStatus()}catch(r){return n.tryCatch.setError(r),n.setLastError(10)}},napi_is_error:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).value instanceof Error?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_is_typedarray:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o=d.handleStore.get(e).isTypedArray()?1:0;return new DataView(a.buffer).setInt8(t,o,!0),n.clearLastError()},napi_strict_equals:function(r,e,t,n){var o;if(!r)return 1;var s=d.envStore.get(r);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{return e&&(t&&n)?(o=d.handleStore.get(e).value===d.handleStore.get(t).value?1:0,new DataView(a.buffer).setInt8(n,o,!0),s.getReturnStatus()):s.setLastError(1)}catch(r){return s.tryCatch.setError(r),s.setLastError(10)}},napi_typeof:function(r,e,t){if(!r)return 1;var n=d.envStore.get(r);if(n.checkGCAccess(),!e)return n.setLastError(1);if(!t)return n.setLastError(1);var o,s=d.handleStore.get(e);if(s.isNumber())o=3;else if(s.isBigInt())o=9;else if(s.isString())o=4;else if(s.isFunction())o=7;else if(s.isExternal())o=8;else if(s.isObject())o=6;else if(s.isBoolean())o=2;else if(s.isUndefined())o=0;else if(s.isSymbol())o=5;else{if(!s.isNull())return n.setLastError(1);o=1}return new DataView(a.buffer).setInt32(t,o,!0),n.clearLastError()}});var br=Object.freeze({__proto__:null,napi_get_version:function(r,e){if(!r)return 1;var t=d.envStore.get(r);return e?(new DataView(a.buffer).setUint32(e,10,!0),t.clearLastError()):t.setLastError(1)}});function Sr(r){for(var e=Object.keys(r),t=0;t<e.length;++t){var n=e[t];0!==n.indexOf("$")&&(0===n.indexOf("emnapi_")?g.imports.emnapi[n]=r[n]:0===n.indexOf("_emnapi_")||"napi_set_last_error"===n||"napi_clear_last_error"===n?g.imports.env[n]=r[n]:g.imports.napi[n]=r[n])}}return U.init(),P.init(),q.init(),z.init(),S.init(),g.emnapi.syncMemory=ir,g.emnapi.getMemoryAddress=ur,Sr(B),Sr(x),Sr(J),Sr(V),Sr($),Sr(K),Sr(lr),Sr(dr),Sr(or),Sr(vr),Sr(fr),Sr(pr),Sr(gr),Sr(yr),Sr(hr),Sr(G),Sr(Er),Sr(wr),Sr(Lr),Sr(mr),Sr(br),g.imports.napi.napi_create_threadsafe_function=function(r,e,t,n,o,u,f,c,l,v,p){if(!r)return 1;var g=d.envStore.get(r);if(g.checkGCAccess(),!n)return g.setLastError(1);if(o>>>=0,0===(u>>>=0))return g.setLastError(1);if(!p)return g.setLastError(1);var y,h=0;if(e){if("function"!=typeof d.handleStore.get(e).value)return g.setLastError(1);h=d.createReference(g,e,1,1).id}else if(!v)return g.setLastError(1);if(t){if(null==(y=d.handleStore.get(t).value))return g.setLastError(2);y=Object(y)}else y={};var E=g.ensureHandleId(y),_=d.handleStore.get(n).value;if("symbol"==typeof _)return g.setLastError(3);_=String(_);var w=g.ensureHandleId(_),L=z.offset.end,m=s(L);if(!m)return g.setLastError(9);new Uint8Array(a.buffer).subarray(m,m+L).fill(0);var b=d.createReference(g,E,1,1),S=b.id,C=new DataView(a.buffer);return C.setInt32(m,S,!0),z.initQueue(m)?(R(E,w,-1,m+z.offset.async_id),C.setUint32(m+z.offset.thread_count,u,!0),C.setInt32(m+z.offset.context,l,!0),C.setUint32(m+z.offset.max_queue_size,o,!0),C.setInt32(m+z.offset.ref,h,!0),C.setInt32(m+z.offset.env,r,!0),C.setInt32(m+z.offset.finalize_data,f,!0),C.setInt32(m+z.offset.finalize_cb,c,!0),C.setInt32(m+z.offset.call_js_cb,v,!0),d.addCleanupHook(g,z.cleanup,m),g.ref(),d.increaseWaitingRequestCounter(),C.setInt32(m+z.offset.async_ref,1,!0),C.setInt32(p,m,!0),g.clearLastError()):(i(m),b.dispose(),g.setLastError(9))},g.imports.napi.napi_get_threadsafe_function_context=function(r,e){if(!r||!e)return l(),1;var t=z.getContext(r);return new DataView(a.buffer).setInt32(e,t,!0),0},g.imports.napi.napi_call_threadsafe_function=function(r,e,t){return r?z.push(r,e,t):(l(),1)},g.imports.napi.napi_acquire_threadsafe_function=function(r){return r?z.getMutex(r).execute((function(){return z.getIsClosing(r)?16:(z.addThreadCount(r),0)})):(l(),1)},g.imports.napi.napi_release_threadsafe_function=function(r,e){if(!r)return l(),1;var t=z.getMutex(r),n=z.getCond(r);return t.execute((function(){if(0===z.getThreadCount(r))return 1;if((z.subThreadCount(r),0===z.getThreadCount(r)||1===e)&&!z.getIsClosing(r)){var t=1===e?1:0;z.setIsClosing(r,t),t&&z.getMaxQueueSize(r)>0&&n.signal(),z.send(r)}return 0}))},g.imports.napi.napi_unref_threadsafe_function=function(r,e){if(!e)return l(),1;var t=e+z.offset.async_ref>>2,n=new Int32Array(a.buffer);return Atomics.load(n,t)&&(Atomics.store(n,t,0),d.decreaseWaitingRequestCounter()),0},g.imports.napi.napi_ref_threadsafe_function=function(r,e){if(!e)return l(),1;var t=e+z.offset.async_ref>>2,n=new Int32Array(a.buffer);return Atomics.load(n,t)||(Atomics.store(n,t,1),d.increaseWaitingRequestCounter()),0},g}();return n}function o(e,t,n,o){const i=(o=null!=o?o:{}).getMemory,u=o.getTable,f=o.beforeInit;if(null!=i&&"function"!=typeof i)throw new TypeError("options.getMemory is not a function");if(null!=u&&"function"!=typeof u)throw new TypeError("options.getTable is not a function");if(null!=f&&"function"!=typeof f)throw new TypeError("options.beforeInit is not a function");let c;const l="object"==typeof t&&null!==t;if(l){if(t.loaded)throw new Error("napiModule has already loaded");c=t}else c=a(o);const d=o.wasi;let v,p={env:c.imports.env,napi:c.imports.napi,emnapi:c.imports.emnapi};d&&(v=new r.WASIThreads(c.childThread?{wasi:d,childThread:!0,postMessage:c.postMessage}:{wasi:d,threadManager:c.PThread,waitThreadStart:c.waitThreadStart}),Object.assign(p,"function"==typeof d.getImportObject?d.getImportObject():{wasi_snapshot_preview1:d.wasiImport}),Object.assign(p,v.getImportObject()));const g=o.overwriteImports;if("function"==typeof g){const r=g(p);"object"==typeof r&&null!==r&&(p=r)}return e(n,p,((r,t)=>{if(r)throw r;const n=t.instance;let a=n;const o=n.exports,g="memory"in o,y="memory"in p.env,h=i?i(o):g?o.memory:y?p.env.memory:void 0;if(!h)throw new Error("memory is neither exported nor imported");const E=u?u(o):o.__indirect_function_table;if(d&&!g){const r=Object.create(null);Object.assign(r,o,{memory:h}),a={exports:r}}const _=t.module;d?a=v.initialize(a,_,h):c.PThread.setup(_,h);const w=()=>{f&&f({instance:n,module:_}),c.init({instance:a,module:_,memory:h,table:E});const r={instance:n,module:_,usedInstance:a};return l||(r.napiModule=c),r};if(c.PThread.shouldPreloadWorkers()){const r=c.PThread.loadWasmModuleToAllWorkers();if(e===s)return r.then(w);throw new Error("Synchronous loading is not supported with worker pool (reuseWorker.size > 0)")}return w()}))}function s(r,e,t){return n(r,e).then((r=>t(null,r)),(r=>t(r)))}function i(r,n,a){let o;try{o=function(r,n){if(!r)throw new TypeError("Invalid wasm source");let a;if(t(n),n=null!=n?n:{},r instanceof ArrayBuffer||ArrayBuffer.isView(r))a=new e.Module(r);else{if(!(r instanceof WebAssembly.Module))throw new TypeError("Invalid wasm source");a=r}return{instance:new e.Instance(a,n),module:a}}(r,n)}catch(r){return a(r)}return a(null,o)}class u extends r.ThreadMessageHandler{constructor(r){if("function"!=typeof r.onLoad)throw new TypeError("options.onLoad is not a function");super(r),this.napiModule=void 0}instantiate(r){const e=this.onLoad(r);return"function"==typeof e.then?e.then((r=>(this.napiModule=r.napiModule,r))):(this.napiModule=e.napiModule,e)}handle(r){var e;if(super.handle(r),null===(e=null==r?void 0:r.data)||void 0===e?void 0:e.__emnapi__){const e=r.data.__emnapi__.type,t=r.data.__emnapi__.payload;"async-worker-init"===e?this.handleAfterLoad(r,(()=>{this.napiModule.initWorker(t.arg)})):"async-work-execute"===e&&this.handleAfterLoad(r,(()=>{this.napiModule.executeAsyncWork(t.work)}))}}}exports.MessageHandler=u,exports.createNapiModule=a,exports.instantiateNapiModule=function(r,e){return o(s,void 0,r,e)},exports.instantiateNapiModuleSync=function(r,e){return o(i,void 0,r,e)},exports.loadNapiModule=function(r,e,t){if("object"!=typeof r||null===r)throw new TypeError("Invalid napiModule");return o(s,r,e,t)},exports.loadNapiModuleSync=function(r,e,t){if("object"!=typeof r||null===r)throw new TypeError("Invalid napiModule");return o(i,r,e,t)},exports.version="1.4.3",Object.keys(r).forEach((function(e){"default"===e||Object.prototype.hasOwnProperty.call(exports,e)||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return r[e]}})}));
