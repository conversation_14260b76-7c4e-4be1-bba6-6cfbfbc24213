!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).wasiThreads={})}(this,(function(e){var t="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0,r="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node;function o(e){return"function"==typeof(null==e?void 0:e.postMessage)?e.postMessage:"function"==typeof postMessage?postMessage:void 0}function n(e){return"function"==typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(e)}function i(e){try{return e instanceof t.RuntimeError}catch(e){return!1}}function a(e,t){return{__emnapi__:{type:e,payload:t}}}function s(e){if(e){if(!n(e.buffer))throw new Error("Multithread features require shared wasm memory. Try to compile with `-matomics -mbulk-memory` and use `--import-memory --shared-memory` during linking, then create WebAssembly.Memory with `shared: true` option")}else if("undefined"==typeof SharedArrayBuffer)throw new Error("Current environment does not support SharedArrayBuffer, threads are not available!")}var d=0,h=function(){function e(e){var t;if(this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.wasmModule=null,this.wasmMemory=null,this.messageEvents=new WeakMap,!e)throw new TypeError("ThreadManager(): options is not provided");this._childThread="childThread"in e&&Boolean(e.childThread),this._childThread?(this._onCreateWorker=void 0,this._reuseWorker=!1,this._beforeLoad=void 0):(this._onCreateWorker=e.onCreateWorker,this._reuseWorker=function(e){var t;if("boolean"==typeof e)return!!e&&{size:0,strict:!1};if("number"==typeof e){if(!(e>=0))throw new RangeError("reuseWorker: size must be a non-negative integer");return{size:e,strict:!1}}if(!e)return!1;var r=null!==(t=Number(e.size))&&void 0!==t?t:0,o=Boolean(e.strict);if(!(r>0)&&o)throw new RangeError("reuseWorker: size must be set to positive integer if strict is set to true");return{size:r,strict:o}}(e.reuseWorker),this._beforeLoad=e.beforeLoad),this.printErr=null!==(t=e.printErr)&&void 0!==t?t:console.error.bind(console)}return Object.defineProperty(e.prototype,"nextWorkerID",{get:function(){return d},enumerable:!1,configurable:!0}),e.prototype.init=function(){this._childThread||this.initMainThread()},e.prototype.initMainThread=function(){this.preparePool()},e.prototype.preparePool=function(){if(this._reuseWorker&&this._reuseWorker.size)for(var e=this._reuseWorker.size;e--;){var t=this.allocateUnusedWorker();r&&(t.once("message",(function(){})),t.unref())}},e.prototype.shouldPreloadWorkers=function(){return!this._childThread&&this._reuseWorker&&this._reuseWorker.size>0},e.prototype.loadWasmModuleToAllWorkers=function(){for(var e=this,t=Array(this.unusedWorkers.length),o=function(e){var o=n.unusedWorkers[e];r&&o.ref(),t[e]=n.loadWasmModuleToWorker(o).then((function(e){return r&&o.unref(),e}),(function(e){throw r&&o.unref(),e}))},n=this,i=0;i<this.unusedWorkers.length;++i)o(i);return Promise.all(t).catch((function(t){throw e.terminateAllThreads(),t}))},e.prototype.preloadWorkers=function(){return this.shouldPreloadWorkers()?this.loadWasmModuleToAllWorkers():Promise.resolve([])},e.prototype.setup=function(e,t){this.wasmModule=e,this.wasmMemory=t},e.prototype.markId=function(e){if(e.__emnapi_tid)return e.__emnapi_tid;var t=d+43;return d=(d+1)%536870869,this.pthreads[t]=e,e.__emnapi_tid=t,t},e.prototype.returnWorkerToPool=function(e){var t=e.__emnapi_tid;void 0!==t&&delete this.pthreads[t],this.unusedWorkers.push(e),this.runningWorkers.splice(this.runningWorkers.indexOf(e),1),delete e.__emnapi_tid,r&&e.unref()},e.prototype.loadWasmModuleToWorker=function(e,t){var o=this;if(e.whenLoaded)return e.whenLoaded;var n=this.printErr,i=this._beforeLoad,d=this;return e.whenLoaded=new Promise((function(h,u){e.onmessage=function(t){!function(t){if(t.__emnapi__){var n=t.__emnapi__.type,i=t.__emnapi__.payload;"loaded"===n?(e.loaded=!0,r&&!e.__emnapi_tid&&e.unref(),h(e)):"cleanup-thread"===n&&i.tid in o.pthreads&&o.cleanThread(e,i.tid)}}(t.data),o.fireMessageEvent(e,t)},e.onerror=function(t){var r="worker sent an error!";if(void 0!==e.__emnapi_tid&&(r="worker (tid = "+e.__emnapi_tid+") sent an error!"),n(r+" "+t.message),-1!==t.message.indexOf("RuntimeError")||-1!==t.message.indexOf("unreachable"))try{d.terminateAllThreads()}catch(e){}throw u(t),t},r&&(e.on("message",(function(t){var r,o;null===(o=(r=e).onmessage)||void 0===o||o.call(r,{data:t})})),e.on("error",(function(t){var r,o;null===(o=(r=e).onerror)||void 0===o||o.call(r,t)})),e.on("detachedExit",(function(){}))),"function"==typeof i&&i(e);try{e.postMessage(a("load",{wasmModule:o.wasmModule,wasmMemory:o.wasmMemory,sab:t}))}catch(e){throw s(o.wasmMemory),e}})),e.whenLoaded},e.prototype.allocateUnusedWorker=function(){var e=this._onCreateWorker;if("function"!=typeof e)throw new TypeError("`options.onCreateWorker` is not provided");var t=e({type:"thread",name:"emnapi-pthread"});return this.unusedWorkers.push(t),t},e.prototype.getNewWorker=function(e){if(this._reuseWorker){if(0===this.unusedWorkers.length){if(this._reuseWorker.strict)if(!r)return void(0,this.printErr)("Tried to spawn a new thread, but the thread pool is exhausted.\nThis might result in a deadlock unless some threads eventually exit or the code explicitly breaks out to the event loop.");var t=this.allocateUnusedWorker();this.loadWasmModuleToWorker(t,e)}return this.unusedWorkers.pop()}var o=this.allocateUnusedWorker();return this.loadWasmModuleToWorker(o,e),this.unusedWorkers.pop()},e.prototype.cleanThread=function(e,t,r){if(!r&&this._reuseWorker)this.returnWorkerToPool(e);else{delete this.pthreads[t];var o=this.runningWorkers.indexOf(e);-1!==o&&this.runningWorkers.splice(o,1),this.terminateWorker(e),delete e.__emnapi_tid}},e.prototype.terminateWorker=function(e){var t,r=this,o=e.__emnapi_tid;e.terminate(),null===(t=this.messageEvents.get(e))||void 0===t||t.clear(),this.messageEvents.delete(e),e.onmessage=function(e){e.data.__emnapi__&&(0,r.printErr)('received "'+e.data.__emnapi__.type+'" command from terminated worker: '+o)}},e.prototype.terminateAllThreads=function(){for(var e=0;e<this.runningWorkers.length;++e)this.terminateWorker(this.runningWorkers[e]);for(e=0;e<this.unusedWorkers.length;++e)this.terminateWorker(this.unusedWorkers[e]);this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.preparePool()},e.prototype.addMessageEventListener=function(e,t){var r=this.messageEvents.get(e);return r||(r=new Set,this.messageEvents.set(e,r)),r.add(t),function(){null==r||r.delete(t)}},e.prototype.fireMessageEvent=function(e,t){var r=this.messageEvents.get(e);if(r){var o=this.printErr;r.forEach((function(e){try{e(t)}catch(e){o(e.stack)}}))}},e}(),u=Symbol("kIsProxy");function l(e,t){if(e[u])return e;var r=e.exports,o=function(e){for(var t=["apply","construct","defineProperty","deleteProperty","get","getOwnPropertyDescriptor","getPrototypeOf","has","isExtensible","ownKeys","preventExtensions","set","setPrototypeOf"],r={},o=function(o){var n=t[o];r[n]=function(){var t=Array.prototype.slice.call(arguments,1);return t.unshift(e),Reflect[n].apply(Reflect,t)}},n=0;n<t.length;n++)o(n);return r}(r),n=function(){},i=function(){return 0};o.get=function(e,o,a){var s;return"memory"===o?null!==(s="function"==typeof t?t():t)&&void 0!==s?s:Reflect.get(r,o,a):"_initialize"===o?o in r?n:void 0:"_start"===o?o in r?i:void 0:Reflect.get(r,o,a)},o.has=function(e,t){return"memory"===t||Reflect.has(r,t)};var a=new Proxy(Object.create(null),o);return new Proxy(e,{get:function(e,t,r){return"exports"===t?a:t===u||Reflect.get(e,t,r)}})}var c=new WeakMap,f=function(){function e(e){var n=this;if(!e)throw new TypeError("WASIThreads(): options is not provided");if(!e.wasi)throw new TypeError("WASIThreads(): options.wasi is not provided");c.set(this,new WeakSet);var d=e.wasi;!function(e,t){var r=c.get(e);if(r.has(t))return;var o=e,n=t.wasiImport;if(n){var a=n.proc_exit;n.proc_exit=function(e){return o.terminateAllThreads(),a.call(this,e)}}var s=t.start;"function"==typeof s&&(t.start=function(e){try{return s.call(this,e)}catch(e){throw i(e)&&o.terminateAllThreads(),e}});r.add(t)}(this,d),this.wasi=d,this.childThread="childThread"in e&&Boolean(e.childThread),this.PThread=void 0,"threadManager"in e?"function"==typeof e.threadManager?this.PThread=e.threadManager():this.PThread=e.threadManager:this.childThread||(this.PThread=new h(e),this.PThread.init());var u=!1;"waitThreadStart"in e&&(u="number"==typeof e.waitThreadStart?e.waitThreadStart:Boolean(e.waitThreadStart));var l=o(e);if(this.childThread&&"function"!=typeof l)throw new TypeError("options.postMessage is not a function");this.postMessage=l;var f=Boolean(e.wasm64),p=function(e){if(e.data.__emnapi__){var t=e.data.__emnapi__.type,r=e.data.__emnapi__.payload;"spawn-thread"===t?m(r.startArg,r.errorOrTid):"terminate-all-threads"===t&&n.terminateAllThreads()}},m=function(e,o){var i,d=void 0!==o;try{s(n.wasmMemory)}catch(e){if(null===(i=n.PThread)||void 0===i||i.printErr(e.stack),d){var h=new Int32Array(n.wasmMemory.buffer,o,2);return Atomics.store(h,0,1),Atomics.store(h,1,6),Atomics.notify(h,1),1}return-6}if(!d){var c=n.wasmInstance.exports.malloc;if(!(o=f?Number(c(BigInt(8))):c(8)))return-48}var m=n.wasmInstance.exports.free,y=f?function(e){m(BigInt(e))}:m,v=new Int32Array(n.wasmMemory.buffer,o,2);if(Atomics.store(v,0,0),Atomics.store(v,1,0),n.childThread){l(a("spawn-thread",{startArg:e,errorOrTid:o})),Atomics.wait(v,1,0);var w=Atomics.load(v,0),_=Atomics.load(v,1);return d?w:(y(o),w?-_:_)}var g,k,T,W=u||0===u;W&&(g=new Int32Array(new SharedArrayBuffer(8208)),Atomics.store(g,0,0));var A=n.PThread;try{if(!(k=A.getNewWorker(g)))throw new Error("failed to get new worker");if(A.addMessageEventListener(k,p),T=A.markId(k),r&&k.ref(),k.postMessage(a("start",{tid:T,arg:e,sab:g})),W){if("number"==typeof u){if("timed-out"===Atomics.wait(g,0,0,u)){try{A.cleanThread(k,T,!0)}catch(e){}throw new Error("Spawning thread timed out. Please check if the worker is created successfully and if message is handled properly in the worker.")}}else Atomics.wait(g,0,0);if(Atomics.load(g,0)>1){try{A.cleanThread(k,T,!0)}catch(e){}throw function(e){var r,o,n=new Int32Array(e);if(Atomics.load(n,0)<=1)return null;var i=Atomics.load(n,1),a=Atomics.load(n,2),s=Atomics.load(n,3),d=new Uint8Array(e),h=d.slice(16,16+i),u=d.slice(16+i,16+i+a),l=d.slice(16+i+a,16+i+a+s),c=(new TextDecoder).decode(h),f=(new TextDecoder).decode(u),p=(new TextDecoder).decode(l),m=new(null!==(r=globalThis[c])&&void 0!==r?r:"RuntimeError"===c&&null!==(o=t.RuntimeError)&&void 0!==o?o:Error)(f);return Object.defineProperty(m,"stack",{value:p,writable:!0,enumerable:!1,configurable:!0}),m}(g.buffer)}}}catch(e){return Atomics.store(v,0,1),Atomics.store(v,1,6),Atomics.notify(v,1),null==A||A.printErr(e.stack),d?1:(y(o),-6)}return Atomics.store(v,0,0),Atomics.store(v,1,T),Atomics.notify(v,1),A.runningWorkers.push(k),W||k.whenLoaded.catch((function(e){throw delete k.whenLoaded,A.cleanThread(k,T,!0),e})),d?0:(y(o),T)};this.threadSpawn=m}return e.prototype.getImportObject=function(){return{wasi:{"thread-spawn":this.threadSpawn}}},e.prototype.setup=function(e,t,r){null!=r||(r=e.exports.memory),this.wasmInstance=e,this.wasmMemory=r,this.PThread&&this.PThread.setup(t,r)},e.prototype.preloadWorkers=function(){return this.PThread?this.PThread.preloadWorkers():Promise.resolve([])},e.prototype.initialize=function(e,t,r){var o=e.exports;null!=r||(r=o.memory),this.childThread&&(e=l(e,r)),this.setup(e,t,r);var n=this.wasi;if("_start"in o&&"function"==typeof o._start)if(this.childThread){n.start(e);try{n[p(n,"kStarted")]=!1}catch(e){}}else!function(e,t){var r=p(e,["kInstance","kSetMemory"]),o=r[0],n=r[1];e[o]=t,e[n](t.exports.memory)}(n,e);else n.initialize(e);return e},e.prototype.start=function(e,t,r){var o=e.exports;return null!=r||(r=o.memory),this.childThread&&(e=l(e,r)),this.setup(e,t,r),{exitCode:this.wasi.start(e),instance:e}},e.prototype.terminateAllThreads=function(){var e;this.childThread?this.postMessage(a("terminate-all-threads",{})):null===(e=this.PThread)||void 0===e||e.terminateAllThreads()},e}();function p(e,t){var r=Object.getOwnPropertySymbols(e),o=function(e){return function(t){return t.description?t.description===e:t.toString()==="Symbol(".concat(e,")")}};return Array.isArray(t)?t.map((function(e){return r.filter(o(e))[0]})):r.filter(o(t))[0]}var m=function(){function e(e){var t=o(e);if("function"!=typeof t)throw new TypeError("options.postMessage is not a function");this.postMessage=t,this.onLoad=null==e?void 0:e.onLoad,this.instance=void 0,this.messagesBeforeLoad=[]}return e.prototype.instantiate=function(e){if("function"==typeof this.onLoad)return this.onLoad(e);throw new Error("ThreadMessageHandler.prototype.instantiate is not implemented")},e.prototype.handle=function(e){var t,r=this;if(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.__emnapi__){var o=e.data.__emnapi__.type,n=e.data.__emnapi__.payload;"load"===o?this._load(n):"start"===o&&this.handleAfterLoad(e,(function(){r._start(n)}))}},e.prototype._load=function(e){var t=this;if(void 0===this.instance){var r;try{r=this.instantiate(e)}catch(t){return void this._loaded(t,null,e)}var o=r&&"then"in r?r.then:void 0;"function"==typeof o?o.call(r,(function(r){t._loaded(null,r,e)}),(function(r){t._loaded(r,null,e)})):this._loaded(null,r,e)}},e.prototype._start=function(e){if("function"!=typeof this.instance.exports.wasi_thread_start){var t=new TypeError("wasi_thread_start is not exported");throw y(e.sab,2,t),t}var r=this.postMessage,o=e.tid,n=e.arg;y(e.sab,1);try{this.instance.exports.wasi_thread_start(o,n)}catch(t){throw i(t)&&r(a("terminate-all-threads",{})),t}r(a("cleanup-thread",{tid:o}))},e.prototype._loaded=function(e,t,r){if(e)throw y(r.sab,2,e),e;if(null==t){var o=new TypeError("onLoad should return an object");throw y(r.sab,2,o),o}var n=t.instance;if(!n){var i=new TypeError('onLoad should return an object which includes "instance"');throw y(r.sab,2,i),i}this.instance=n,(0,this.postMessage)(a("loaded",{}));var s=this.messagesBeforeLoad;this.messagesBeforeLoad=[];for(var d=0;d<s.length;d++){var h=s[d];this.handle({data:h})}},e.prototype.handleAfterLoad=function(e,t){void 0!==this.instance?t.call(this,e):this.messagesBeforeLoad.push(e.data)},e}();function y(e,t,r){e&&(!function(e,t,r){var o=new Int32Array(e);if(Atomics.store(o,0,t),t>1&&r){var n=r.name,i=r.message,a=r.stack,s=(new TextEncoder).encode(n),d=(new TextEncoder).encode(i),h=(new TextEncoder).encode(a);Atomics.store(o,1,s.length),Atomics.store(o,2,d.length),Atomics.store(o,3,h.length);var u=new Uint8Array(e);u.set(s,16),u.set(d,16+s.length),u.set(h,16+s.length+d.length)}}(e.buffer,t,r),Atomics.notify(e,0))}e.ThreadManager=h,e.ThreadMessageHandler=m,e.WASIThreads=f,e.createInstanceProxy=l,e.isSharedArrayBuffer=n,e.isTrapError=i}));
